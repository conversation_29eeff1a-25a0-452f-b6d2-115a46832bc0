'use client';

import { useLanguage } from '../../lib/LanguageContext';

export default function TermsPage() {
  const { language } = useLanguage();

  const content = {
    it: {
      title: "Termini di Servizio",
      lastUpdated: "Ultimo aggiornamento: ",
      sections: [
        {
          title: "1. Accettazione dei Termini",
          content: "Utilizzando FM Challenger, accetti di essere vincolato da questi termini di servizio. Se non accetti questi termini, non utilizzare il nostro servizio."
        },
        {
          title: "2. Descrizione del Servizio",
          content: "FM Challenger è un generatore di sfide per Football Manager che fornisce combinazioni casuali di squadre e obiettivi per migliorare la tua esperienza di gioco."
        },
        {
          title: "3. Uso Accettabile",
          content: "Ti impegni a utilizzare il servizio solo per scopi legali e in conformità con tutti i termini e condizioni applicabili."
        },
        {
          title: "4. Proprietà Intellettuale",
          content: "Tutti i contenuti, marchi e proprietà intellettuale su FM Challenger sono di proprietà dei rispettivi proprietari."
        },
        {
          title: "5. Limitazione di Responsabilità",
          content: "FM Challenger è fornito 'così com'è' senza garanzie di alcun tipo. Non siamo responsabili per eventuali danni derivanti dall'uso del servizio."
        }
      ]
    },
    en: {
      title: "Terms of Service",
      lastUpdated: "Last updated: ",
      sections: [
        {
          title: "1. Acceptance of Terms",
          content: "By using FM Challenger, you agree to be bound by these terms of service. If you do not agree to these terms, do not use our service."
        },
        {
          title: "2. Service Description",
          content: "FM Challenger is a challenge generator for Football Manager that provides random combinations of teams and objectives to enhance your gaming experience."
        },
        {
          title: "3. Acceptable Use",
          content: "You agree to use the service only for lawful purposes and in accordance with all applicable terms and conditions."
        },
        {
          title: "4. Intellectual Property",
          content: "All content, trademarks, and intellectual property on FM Challenger are owned by their respective owners."
        },
        {
          title: "5. Limitation of Liability",
          content: "FM Challenger is provided 'as is' without warranties of any kind. We are not responsible for any damages arising from the use of the service."
        }
      ]
    }
  };

  const texts = content[language] || content.it;

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="card">
          <h1 className="text-3xl font-bold text-textTitle mb-4 gradient-text">
            {texts.title}
          </h1>
          
          <p className="text-textSecondary mb-8">
            {texts.lastUpdated}{new Date().toLocaleDateString(language === 'it' ? 'it-IT' : 'en-US')}
          </p>

          <div className="space-y-8">
            {texts.sections.map((section, index) => (
              <div key={index} className="border-l-4 border-accent pl-6">
                <h2 className="text-xl font-semibold text-textTitle mb-3">
                  {section.title}
                </h2>
                <p className="text-textSecondary leading-relaxed">
                  {section.content}
                </p>
              </div>
            ))}
          </div>

          <div className="mt-12 pt-8 border-t border-accent/20">
            <p className="text-textSecondary text-sm">
              {language === 'it' 
                ? 'Per domande riguardo questi termini, contattaci a: <EMAIL>'
                : 'For questions about these terms, contact us at: <EMAIL>'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
