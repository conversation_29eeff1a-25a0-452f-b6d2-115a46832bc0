'use client';

import { useState } from 'react';
import { useLanguage } from '../../lib/LanguageContext';

export default function FeedbackPage() {
  const { language } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
    challengeType: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const content = {
    it: {
      title: "Feedback e Suggerimenti",
      subtitle: "Aiutaci a migliorare FM Challenger con le tue idee!",
      form: {
        name: "No<PERSON>",
        email: "Email",
        message: "Messaggio",
        challengeType: "Tipo di feedback",
        challengeTypes: {
          objective: "Nuovi obiettivi",
          squad: "Sfide rosa",
          tactics: "Sfide tattiche",
          team: "Nuove squadre",
          feature: "Nuove funzionalità",
          bug: "Segnalazione bug",
          other: "Altro"
        },
        send: "Invia Feedback",
        sending: "Invio in corso...",
        success: "Feedback inviato con successo! Grazie per il tuo contributo.",
        error: "Errore nell'invio del feedback. Riprova più tardi."
      },
      info: {
        title: "Come possiamo aiutarti?",
        description: "Il tuo feedback è prezioso per noi. Che tu abbia idee per nuove sfide, suggerimenti per miglioramenti o segnalazioni di bug, siamo qui per ascoltarti.",
        examples: "Esempi di feedback utili:",
        examplesList: [
          "Nuovi obiettivi per squadre di Serie B",
          "Sfide specifiche per giovani allenatori",
          "Miglioramenti all'interfaccia utente",
          "Nuove opzioni di filtro",
          "Segnalazioni di errori o problemi"
        ]
      }
    },
    en: {
      title: "Feedback and Suggestions",
      subtitle: "Help us improve FM Challenger with your ideas!",
      form: {
        name: "Name",
        email: "Email",
        message: "Message",
        challengeType: "Feedback type",
        challengeTypes: {
          objective: "New objectives",
          squad: "Squad challenges",
          tactics: "Tactical challenges",
          team: "New teams",
          feature: "New features",
          bug: "Bug report",
          other: "Other"
        },
        send: "Send Feedback",
        sending: "Sending...",
        success: "Feedback sent successfully! Thank you for your contribution.",
        error: "Error sending feedback. Please try again later."
      },
      info: {
        title: "How can we help you?",
        description: "Your feedback is valuable to us. Whether you have ideas for new challenges, suggestions for improvements, or bug reports, we're here to listen.",
        examples: "Examples of useful feedback:",
        examplesList: [
          "New objectives for Serie B teams",
          "Specific challenges for young managers",
          "User interface improvements",
          "New filter options",
          "Error or issue reports"
        ]
      }
    }
  };

  const texts = content[language] || content.it;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          message: '',
          challengeType: ''
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-textTitle mb-4 gradient-text">
            {texts.title}
          </h1>
          <p className="text-textSecondary text-lg max-w-2xl mx-auto">
            {texts.subtitle}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Form */}
          <div className="card">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-textPrimary mb-2">
                  {texts.form.name}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-textPrimary mb-2">
                  {texts.form.email}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div>
                <label htmlFor="challengeType" className="block text-sm font-medium text-textPrimary mb-2">
                  {texts.form.challengeType}
                </label>
                <select
                  id="challengeType"
                  name="challengeType"
                  value={formData.challengeType}
                  onChange={handleChange}
                  required
                  className="select-custom w-full"
                >
                  <option value="">Seleziona...</option>
                  {Object.entries(texts.form.challengeTypes).map(([key, value]) => (
                    <option key={key} value={key}>{value}</option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-textPrimary mb-2">
                  {texts.form.message}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="input-field w-full resize-none"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? texts.form.sending : texts.form.send}
              </button>

              {submitStatus === 'success' && (
                <div className="bg-success/20 border border-success/50 text-success p-4 rounded-lg">
                  {texts.form.success}
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="bg-error/20 border border-error/50 text-red-200 p-4 rounded-lg">
                  {texts.form.error}
                </div>
              )}
            </form>
          </div>

          {/* Info */}
          <div className="card">
            <h3 className="text-xl font-bold text-textTitle mb-4">
              {texts.info.title}
            </h3>
            <p className="text-textSecondary mb-6">
              {texts.info.description}
            </p>

            <h4 className="text-lg font-semibold text-textTitle mb-3">
              {texts.info.examples}
            </h4>
            <ul className="space-y-2">
              {texts.info.examplesList.map((example, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-accent mr-2">•</span>
                  <span className="text-textSecondary">{example}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
