/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/CookieBanner.jsx */ \"(rsc)/./components/CookieBanner.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GoogleAnalytics.jsx */ \"(rsc)/./components/GoogleAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SessionProvider.jsx */ \"(rsc)/./components/SessionProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Topbar.jsx */ \"(rsc)/./components/Topbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/LanguageContext.js */ \"(rsc)/./lib/LanguageContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/CookieBanner.jsx */ \"(ssr)/./components/CookieBanner.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GoogleAnalytics.jsx */ \"(ssr)/./components/GoogleAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SessionProvider.jsx */ \"(ssr)/./components/SessionProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Topbar.jsx */ \"(ssr)/./components/Topbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/LanguageContext.js */ \"(ssr)/./lib/LanguageContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(rsc)/./app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3BhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNJQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxhcHBcXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.js */ \"(ssr)/./app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3BhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNJQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxhcHBcXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AboutSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AboutSection */ \"(ssr)/./components/AboutSection.jsx\");\n/* harmony import */ var _components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SelectChallenge */ \"(ssr)/./components/SelectChallenge.jsx\");\n/* harmony import */ var _components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ShowChallenge */ \"(ssr)/./components/ShowChallenge.jsx\");\n/* harmony import */ var _components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/SocialAndFeedback */ \"(ssr)/./components/SocialAndFeedback.jsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/supabase */ \"(ssr)/./lib/supabase.js\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__.useLanguage)();\n    const [team, setTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [challenges, setChallenges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDifficulties, setSelectedDifficulties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: '',\n        challenge: ''\n    });\n    // Funzione per tracciare eventi GA\n    const trackEvent = (eventName, eventParams = {})=>{\n        if (false) {}\n    };\n    const generateChallenge = async (teamDifficulty, challengeDifficulty, lang = 'it', continents = [])=>{\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di generazione sfida\n        trackEvent('generate_challenge', {\n            team_difficulty: teamDifficulty,\n            challenge_difficulty: challengeDifficulty,\n            language: lang\n        });\n        // Salva le difficoltà selezionate per poterle riutilizzare durante la rigenerazione\n        setSelectedDifficulties({\n            team: teamDifficulty,\n            challenge: challengeDifficulty,\n            continents: continents\n        });\n        try {\n            console.log(`Generating challenge with team difficulty: ${teamDifficulty}, challenge difficulty: ${challengeDifficulty}, language: ${lang}, continents: ${continents.join(', ')}`);\n            // Get random team based on difficulty and continents\n            const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(teamDifficulty, continents);\n            // Get random challenges based on difficulty\n            const randomChallenges = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getChallenges)(challengeDifficulty, lang);\n            if (!randomTeam) {\n                throw new Error(t.errorTeam);\n            }\n            if (!randomChallenges || Object.keys(randomChallenges).length === 0) {\n                throw new Error(t.errorChallenge);\n            }\n            setTeam(randomTeam);\n            setChallenges(randomChallenges);\n        } catch (err) {\n            console.error('Error generating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stato per tenere traccia degli elementi bloccati\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: false,\n        obiettivi: false,\n        rosa: false,\n        tattica: false\n    });\n    const regenerateChallenge = async (currentLockedItems)=>{\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di rigenerazione sfida\n        trackEvent('regenerate_challenge', {\n            team_difficulty: selectedDifficulties.team,\n            challenge_difficulty: selectedDifficulties.challenge,\n            language: language\n        });\n        // Aggiorniamo lo stato dei lock con quello corrente\n        setLockedItems(currentLockedItems);\n        try {\n            let newTeam = team;\n            let newChallenges = {\n                ...challenges\n            };\n            // If team is not locked, get a new random team\n            if (!currentLockedItems.team && team) {\n                // Usa la difficoltà e i continenti salvati in precedenza\n                const continents = selectedDifficulties.continents || [];\n                const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(selectedDifficulties.team, continents);\n                if (randomTeam) {\n                    newTeam = randomTeam;\n                }\n            }\n            // For each challenge category that is not locked, get a new random challenge\n            for (const category of [\n                'obiettivi',\n                'rosa',\n                'tattica'\n            ]){\n                if (!currentLockedItems[category] && challenges && challenges[category]) {\n                    try {\n                        // Utilizziamo la funzione esistente per ottenere una nuova sfida casuale\n                        const newChallenge = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomChallenge)(selectedDifficulties.challenge, category, language);\n                        if (newChallenge && newChallenge.id !== challenges[category].id) {\n                            newChallenges[category] = newChallenge;\n                        }\n                    } catch (err) {\n                        console.error(`Error regenerating challenge for category ${category}:`, err);\n                    }\n                }\n            }\n            setTeam(newTeam);\n            setChallenges(newChallenges);\n        } catch (err) {\n            console.error('Error regenerating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const shareChallenge = async ()=>{\n        // Traccia l'evento di condivisione sfida\n        trackEvent('share_challenge', {\n            team_name: team?.name || 'unknown',\n            language: language\n        });\n    // Resto del codice esistente...\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-bgMain text-textPrimary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg p-6 md:p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8 \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg p-6 md:p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 grid lg:grid-cols-2 gap-8 border-t border-textSecondary/10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onGenerateChallenge: generateChallenge\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-[400px] flex flex-col justify-center\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-textPrimary bg-bgBox\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-accent\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        t.loading\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"h-5 w-5 text-red-400\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            team && challenges && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                team: team,\n                                                challenges: challenges,\n                                                onRegenerate: regenerateChallenge,\n                                                initialLockedState: lockedItems\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            !team && !challenges && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-textSecondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1,\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg\",\n                                                            children: t.generateChallengeToStart\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 border-t border-textSecondary/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-secondary rounded-xl p-6 border-t border-textSecondary/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary text-sm\",\n                            children: [\n                                \"FM Challenger \\xa9 \",\n                                new Date().getFullYear(),\n                                \" - \",\n                                t.footer\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex justify-center space-x-4 text-xs text-textSecondary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/privacy\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Privacy Policy' : 'Privacy Policy'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/terms\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Termini di Servizio' : 'Terms of Service'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/contact\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Contatti' : 'Contact'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.js\n");

/***/ }),

/***/ "(ssr)/./components/AboutSection.jsx":
/*!*************************************!*\
  !*** ./components/AboutSection.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AboutSection() {\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl md:text-4xl font-bold text-textTitle mb-6 gradient-text\",\n                        children: t.welcomeTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-textSecondary text-lg mb-8 max-w-4xl mx-auto\",\n                        children: t.welcomeSubtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-bgBox rounded-lg p-6 border-l-4 border-accent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-accent mb-4\",\n                                children: t.teamDifficultyTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-textSecondary leading-relaxed\",\n                                children: t.teamDifficultyDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-bgBox rounded-lg p-6 border-l-4 border-accent\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-accent mb-4\",\n                                children: t.challengeDifficultyTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-textSecondary leading-relaxed\",\n                                children: t.challengeDifficultyDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-textSecondary leading-relaxed max-w-4xl mx-auto\",\n                    children: t.finalDescription\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\AboutSection.jsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AboutSection.jsx\n");

/***/ }),

/***/ "(ssr)/./components/CookieBanner.jsx":
/*!*************************************!*\
  !*** ./components/CookieBanner.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CookieBanner = ()=>{\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to clear corrupted data\n    const clearCookieData = ()=>{\n        if (false) {}\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieBanner.useEffect\": ()=>{\n            // Check if localStorage is available and user has already made a choice\n            if (false) {} else {\n                // If localStorage is not available, show banner\n                setShowBanner(true);\n            }\n        }\n    }[\"CookieBanner.useEffect\"], []);\n    const updateGoogleConsent = (consentData)=>{\n        if (false) {}\n    };\n    const saveConsent = (consent)=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Error saving cookie consent:', error);\n        }\n        return false;\n    };\n    const handleAcceptAll = ()=>{\n        const consent = {\n            analytics: true,\n            advertising: true,\n            functional: true,\n            timestamp: Date.now()\n        };\n        saveConsent(consent);\n    };\n    const handleRejectAll = ()=>{\n        const consent = {\n            analytics: false,\n            advertising: false,\n            functional: true,\n            timestamp: Date.now()\n        };\n        saveConsent(consent);\n    };\n    const handleCustomize = (customConsent)=>{\n        const consent = {\n            ...customConsent,\n            functional: true,\n            timestamp: Date.now()\n        };\n        if (saveConsent(consent)) {\n            setShowDetails(false);\n        }\n    };\n    if (!showBanner) return null;\n    const cookieTexts = {\n        it: {\n            title: \"Utilizziamo i cookie\",\n            description: \"Utilizziamo cookie e tecnologie simili per migliorare la tua esperienza, analizzare il traffico e personalizzare i contenuti. Puoi scegliere quali cookie accettare.\",\n            acceptAll: \"Accetta tutti\",\n            rejectAll: \"Rifiuta tutti\",\n            customize: \"Personalizza\",\n            save: \"Salva preferenze\",\n            necessary: \"Cookie necessari\",\n            necessaryDesc: \"Essenziali per il funzionamento del sito\",\n            analytics: \"Cookie analitici\",\n            analyticsDesc: \"Ci aiutano a capire come utilizzi il sito\",\n            advertising: \"Cookie pubblicitari\",\n            advertisingDesc: \"Utilizzati per mostrare annunci pertinenti\"\n        },\n        en: {\n            title: \"We use cookies\",\n            description: \"We use cookies and similar technologies to improve your experience, analyze traffic and personalize content. You can choose which cookies to accept.\",\n            acceptAll: \"Accept all\",\n            rejectAll: \"Reject all\",\n            customize: \"Customize\",\n            save: \"Save preferences\",\n            necessary: \"Necessary cookies\",\n            necessaryDesc: \"Essential for the website to function\",\n            analytics: \"Analytics cookies\",\n            analyticsDesc: \"Help us understand how you use the site\",\n            advertising: \"Advertising cookies\",\n            advertisingDesc: \"Used to show relevant ads\"\n        }\n    };\n    const texts = cookieTexts[language] || cookieTexts.it;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-bgBox border-t border-textSecondary/20 p-4 md:p-6 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: !showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row items-start md:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-textPrimary mb-2\",\n                                        children: texts.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-textSecondary text-sm\",\n                                        children: texts.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-2 w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRejectAll,\n                                        className: \"px-4 py-2 text-textSecondary border border-textSecondary/30 rounded-md hover:bg-textSecondary/10 transition-colors\",\n                                        children: texts.rejectAll\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDetails(true),\n                                        className: \"px-4 py-2 text-accent border border-accent rounded-md hover:bg-accent/10 transition-colors\",\n                                        children: texts.customize\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptAll,\n                                        className: \"px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors\",\n                                        children: texts.acceptAll\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CookieDetails, {\n                        texts: texts,\n                        onSave: handleCustomize,\n                        onBack: ()=>setShowDetails(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst CookieDetails = ({ texts, onSave, onBack })=>{\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        analytics: false,\n        advertising: false\n    });\n    const handleSave = ()=>{\n        onSave(preferences);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-textPrimary\",\n                        children: texts.customize\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"text-textSecondary hover:text-textPrimary\",\n                        children: \"←\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.necessary\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.necessaryDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-500 font-medium\",\n                                children: \"ON\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.analytics\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.analyticsDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"relative inline-flex items-center cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: preferences.analytics,\n                                        onChange: (e)=>setPreferences((prev)=>({\n                                                    ...prev,\n                                                    analytics: e.target.checked\n                                                })),\n                                        className: \"sr-only peer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.advertising\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.advertisingDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"relative inline-flex items-center cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: preferences.advertising,\n                                        onChange: (e)=>setPreferences((prev)=>({\n                                                    ...prev,\n                                                    advertising: e.target.checked\n                                                })),\n                                        className: \"sr-only peer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 pt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleSave,\n                    className: \"flex-1 px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors\",\n                    children: texts.save\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieBanner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CookieBanner.jsx\n");

/***/ }),

/***/ "(ssr)/./components/GoogleAnalytics.jsx":
/*!****************************************!*\
  !*** ./components/GoogleAnalytics.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoogleAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction GoogleAnalyticsInner({ GA_MEASUREMENT_ID }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"GoogleAnalyticsInner.useEffect\": ()=>{\n            if (pathname && window.gtag) {\n                window.gtag('config', GA_MEASUREMENT_ID, {\n                    page_path: pathname\n                });\n            }\n        }\n    }[\"GoogleAnalyticsInner.useEffect\"], [\n        pathname,\n        searchParams,\n        GA_MEASUREMENT_ID\n    ]);\n    return null;\n}\nfunction GoogleAnalytics({ GA_MEASUREMENT_ID }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                strategy: \"afterInteractive\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics-consent\",\n                strategy: \"beforeInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n\n            // Initialize Google Consent Mode v2\n            gtag('consent', 'default', {\n              'analytics_storage': 'denied',\n              'ad_storage': 'denied',\n              'ad_user_data': 'denied',\n              'ad_personalization': 'denied',\n              'functionality_storage': 'granted',\n              'security_storage': 'granted'\n            });\n\n            gtag('js', new Date());\n          `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics\",\n                strategy: \"afterInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n            gtag('config', '${GA_MEASUREMENT_ID}', {\n              page_path: window.location.pathname,\n            });\n          `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                fallback: null,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleAnalyticsInner, {\n                    GA_MEASUREMENT_ID: GA_MEASUREMENT_ID\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/GoogleAnalytics.jsx\n");

/***/ }),

/***/ "(ssr)/./components/LanguageSelector.jsx":
/*!*****************************************!*\
  !*** ./components/LanguageSelector.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LanguageSelector() {\n    const { language, changeLanguage } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'it',\n            name: 'Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'en',\n            name: 'English',\n            flag: '🇬🇧'\n        }\n    ];\n    const currentLanguage = languages.find((lang)=>lang.code === language);\n    const handleLanguageChange = (langCode)=>{\n        changeLanguage(langCode);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-md bg-dropdownBg text-textPrimary hover:bg-bgBox transition-colors\",\n                \"aria-label\": \"Select language\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentLanguage?.flag\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: currentLanguage?.code.toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-40 bg-dropdownBg rounded-md shadow-lg py-1 z-10 border border-textSecondary/20\",\n                children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleLanguageChange(lang.code),\n                        className: `w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:bg-bgBox transition-colors ${language === lang.code ? 'bg-accent/20 text-accent' : 'text-textPrimary'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: lang.flag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: lang.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, lang.code, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LanguageSelector.jsx\n");

/***/ }),

/***/ "(ssr)/./components/LockableItem.jsx":
/*!*************************************!*\
  !*** ./components/LockableItem.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LockableItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LockableItem({ title, content, isLocked, onToggleLock }) {\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: onToggleLock,\n        className: `\n        flex items-center justify-between p-3 rounded-md mb-2 cursor-pointer\n        transition-all duration-200 ease-in-out\n        ${isLocked ? 'border-2 border-accent bg-accent/10 shadow-md' : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80'}\n      `,\n        \"aria-label\": isLocked ? `Sblocca questo elemento` : `Blocca questo elemento`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-medium text-textTitle flex items-center\",\n                        children: [\n                            title,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm opacity-70\",\n                                children: isLocked ? `(${t.locked})` : `(${t.clickToLock})`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-textPrimary\",\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"ml-4 text-xl\",\n                children: isLocked ? '🔒' : '🔓'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LockableItem.jsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LockableItem.jsx\n");

/***/ }),

/***/ "(ssr)/./components/SelectChallenge.jsx":
/*!****************************************!*\
  !*** ./components/SelectChallenge.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SelectChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/supabase */ \"(ssr)/./lib/supabase.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SelectChallenge({ onGenerateChallenge }) {\n    const [teamDifficulty, setTeamDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [challengeDifficulty, setChallengeDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamDifficultyLevels, setTeamDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [challengeDifficultyLevels, setChallengeDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTeamDescription, setSelectedTeamDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChallengeDescription, setSelectedChallengeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedContinents, setSelectedContinents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    // Lista dei continenti disponibili\n    const continents = [\n        {\n            value: 'Europe',\n            label: 'Europa'\n        },\n        {\n            value: 'Asia',\n            label: 'Asia'\n        },\n        {\n            value: 'North America',\n            label: 'Nord America'\n        },\n        {\n            value: 'South America',\n            label: 'Sud America'\n        },\n        {\n            value: 'Africa',\n            label: 'Africa'\n        }\n    ];\n    // Carica i livelli di difficoltà dal database quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SelectChallenge.useEffect\": ()=>{\n            const fetchDifficultyLevels = {\n                \"SelectChallenge.useEffect.fetchDifficultyLevels\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Recupera i livelli di difficoltà per le squadre\n                        const teamLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getTeamDifficultyLevels)(language);\n                        setTeamDifficultyLevels(teamLevels);\n                        // Recupera i livelli di difficoltà per le sfide\n                        const challengeLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getChallengeDifficultyLevels)(language);\n                        setChallengeDifficultyLevels(challengeLevels);\n                        // Resetta le selezioni quando cambia la lingua\n                        setTeamDifficulty('');\n                        setChallengeDifficulty('');\n                        setSelectedTeamDescription('');\n                        setSelectedChallengeDescription('');\n                    } catch (error) {\n                        console.error('Error fetching difficulty levels:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SelectChallenge.useEffect.fetchDifficultyLevels\"];\n            fetchDifficultyLevels();\n        }\n    }[\"SelectChallenge.useEffect\"], [\n        language\n    ]);\n    // Aggiorna la descrizione quando cambia la difficoltà della squadra\n    const handleTeamDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setTeamDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = teamDifficultyLevels.find((level)=>level.value === value);\n        setSelectedTeamDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Aggiorna la descrizione quando cambia la difficoltà della sfida\n    const handleChallengeDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setChallengeDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = challengeDifficultyLevels.find((level)=>level.value === value);\n        setSelectedChallengeDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Gestisce il cambio di stato dei checkbox dei continenti\n    const handleContinentChange = (continent)=>{\n        setSelectedContinents((prev)=>{\n            if (prev.includes(continent)) {\n                return prev.filter((c)=>c !== continent);\n            } else {\n                return [\n                    ...prev,\n                    continent\n                ];\n            }\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (teamDifficulty && challengeDifficulty) {\n            onGenerateChallenge(teamDifficulty, challengeDifficulty, language, selectedContinents);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-4 text-textTitle\",\n                children: t.teamDifficulty\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: t.loading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"teamDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.teamDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"teamDifficulty\",\n                                value: teamDifficulty,\n                                onChange: handleTeamDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.teamDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    teamDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            selectedTeamDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedTeamDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"challengeDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.challengeDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"challengeDifficulty\",\n                                value: challengeDifficulty,\n                                onChange: handleChallengeDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.challengeDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    challengeDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            selectedChallengeDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedChallengeDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"block mb-2 text-textPrimary font-medium\",\n                                children: t.filterByContinent\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                                children: continents.map((continent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: `continent-${continent.value}`,\n                                                checked: selectedContinents.includes(continent.value),\n                                                onChange: ()=>handleContinentChange(continent.value),\n                                                className: \"mr-2 h-4 w-4 accent-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: `continent-${continent.value}`,\n                                                className: \"text-textPrimary text-sm\",\n                                                children: continent.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, continent.value, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            selectedContinents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-xs text-textSecondary\",\n                                children: t.activeFilter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"btn-primary w-full mt-6\",\n                        disabled: !teamDifficulty || !challengeDifficulty,\n                        children: t.generateChallenge\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SelectChallenge.jsx\n");

/***/ }),

/***/ "(ssr)/./components/SessionProvider.jsx":
/*!****************************************!*\
  !*** ./components/SessionProvider.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SessionProvider.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTZFO0FBRTlELFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQUVDLE9BQU8sRUFBRTtJQUMzRCxxQkFDRSw4REFBQ0YsNERBQXVCQTtRQUFDRSxTQUFTQTtrQkFDL0JEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxQcm9nZXR0aVxcZm0tY2hhbGxlbmdlclxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcY29tcG9uZW50c1xcU2Vzc2lvblByb3ZpZGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2Vzc2lvblByb3ZpZGVyKHsgY2hpbGRyZW4sIHNlc3Npb24gfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgc2Vzc2lvbj17c2Vzc2lvbn0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNlc3Npb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./components/ShowChallenge.jsx":
/*!**************************************!*\
  !*** ./components/ShowChallenge.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShowChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _LockableItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LockableItem */ \"(ssr)/./components/LockableItem.jsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html2canvas */ \"(ssr)/./node_modules/html2canvas/dist/html2canvas.esm.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ShowChallenge({ team, challenges, onRegenerate, initialLockedState }) {\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const outputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveSuccess, setSaveSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Inizializza lo stato dei lock con i valori passati dal componente padre o con valori predefiniti\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: initialLockedState?.team || false,\n        obiettivi: initialLockedState?.obiettivi || false,\n        rosa: initialLockedState?.rosa || false,\n        tattica: initialLockedState?.tattica || false\n    });\n    // Aggiorna lo stato dei lock quando cambiano i valori iniziali\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShowChallenge.useEffect\": ()=>{\n            if (initialLockedState) {\n                setLockedItems(initialLockedState);\n            }\n        }\n    }[\"ShowChallenge.useEffect\"], [\n        initialLockedState\n    ]);\n    const toggleLock = (item)=>{\n        setLockedItems((prev)=>({\n                ...prev,\n                [item]: !prev[item]\n            }));\n    };\n    const handleRegenerate = ()=>{\n        // Passa lo stato corrente dei lock alla funzione di rigenerazione\n        onRegenerate(lockedItems);\n    };\n    const handleShare = async ()=>{\n        if (!outputRef.current) return;\n        try {\n            // Crea un canvas dall'elemento output\n            const canvas = await (0,html2canvas__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(outputRef.current, {\n                backgroundColor: '#0D1117',\n                scale: 2,\n                logging: false,\n                useCORS: true\n            });\n            // Converti il canvas in un'immagine\n            const image = canvas.toDataURL('image/jpeg', 0.9);\n            // Crea un link per scaricare l'immagine\n            const link = document.createElement('a');\n            link.href = image;\n            // Crea il nome del file con data e ora\n            const date = new Date();\n            const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}-${String(date.getMinutes()).padStart(2, '0')}`;\n            link.download = `FM-Challenger-${formattedDate}.jpg`;\n            // Simula il click sul link per scaricare l'immagine\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error generating image:', error);\n        }\n    };\n    const handleSaveChallenge = async ()=>{\n        if (!session) return;\n        setSaving(true);\n        setSaveSuccess(false);\n        setSaveError('');\n        try {\n            const response = await fetch('/api/challenges/save', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    team,\n                    challenges\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Errore durante il salvataggio della sfida');\n            }\n            setSaveSuccess(true);\n            // Reset del messaggio di successo dopo 3 secondi\n            setTimeout(()=>{\n                setSaveSuccess(false);\n            }, 3000);\n        } catch (error) {\n            console.error('Error saving challenge:', error);\n            setSaveError(error.message || 'Errore durante il salvataggio della sfida');\n            // Reset del messaggio di errore dopo 3 secondi\n            setTimeout(()=>{\n                setSaveError('');\n            }, 3000);\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (!team || !challenges) {\n        return null;\n    }\n    // Aggiungi questo CSS per i bordi colorati\n    const challengeStyles = {\n        objective: {\n            titleColor: 'text-green-500',\n            borderColor: 'border-l-4 border-green-500'\n        },\n        squad: {\n            titleColor: 'text-yellow-500',\n            borderColor: 'border-l-4 border-yellow-500'\n        },\n        tactics: {\n            titleColor: 'text-red-500',\n            borderColor: 'border-l-4 border-red-500'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"output\",\n            ref: outputRef,\n            className: \"card mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold mb-4 text-textTitle\",\n                    children: t.yourChallenge\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.team\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>toggleLock('team'),\n                            className: `\n              flex items-center space-x-4 p-3 rounded-md mb-2 cursor-pointer\n              transition-all duration-200 ease-in-out\n              ${lockedItems.team ? 'border-2 border-accent bg-accent/10 shadow-md' : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80'}\n            `,\n                            \"aria-label\": lockedItems.team ? \"Sblocca squadra\" : \"Blocca squadra\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-16 h-16 flex-shrink-0\",\n                                    children: team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: team.logo,\n                                        alt: `Logo ${team.name}`,\n                                        fill: true,\n                                        style: {\n                                            objectFit: 'contain'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-textTitle flex items-center\",\n                                            children: [\n                                                team.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-sm opacity-70\",\n                                                    children: lockedItems.team ? `(${t.locked})` : `(${t.clickToLock})`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textPrimary text-sm\",\n                                            children: [\n                                                team.country,\n                                                \" - \",\n                                                team.league\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 text-xl\",\n                                    children: lockedItems.team ? '🔒' : '🔓'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.challenges\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        challenges.obiettivi && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.objective.titleColor,\n                                children: t.objective\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 193,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.obiettivi.text,\n                            isLocked: lockedItems.obiettivi,\n                            onToggleLock: ()=>toggleLock('obiettivi'),\n                            className: `mb-4 ${challengeStyles.objective.borderColor}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        challenges.rosa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.squad.titleColor,\n                                children: t.squad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.rosa.text,\n                            isLocked: lockedItems.rosa,\n                            onToggleLock: ()=>toggleLock('rosa'),\n                            className: `mb-4 ${challengeStyles.squad.borderColor}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        challenges.tattica && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.tactics.titleColor,\n                                children: t.tactics\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.tattica.text,\n                            isLocked: lockedItems.tattica,\n                            onToggleLock: ()=>toggleLock('tattica'),\n                            className: `mb-4 ${challengeStyles.tactics.borderColor}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                saveSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-green-900/20 border border-green-500/50 text-green-200 rounded-md\",\n                    children: \"Sfida salvata con successo!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this),\n                saveError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-900/20 border border-red-500/50 text-red-200 rounded-md\",\n                    children: saveError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex ${session ? 'flex-col sm:flex-row' : 'flex-col sm:flex-row'} gap-3`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRegenerate,\n                            className: \"btn-primary flex-1\",\n                            children: t.regenerateChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleShare,\n                            className: \"flex-1 bg-btnShare hover:bg-btnShareHover text-textPrimary py-3 px-6 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-glow-share transform hover:scale-105\",\n                            children: t.shareChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSaveChallenge,\n                            disabled: saving,\n                            className: \"btn-primary flex-1 bg-green-600 hover:bg-green-700\",\n                            children: [\n                                saving ? 'Salvataggio...' : 'Salva sfida',\n                                \" \\uD83D\\uDCBE\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ShowChallenge.jsx\n");

/***/ }),

/***/ "(ssr)/./components/SocialAndFeedback.jsx":
/*!******************************************!*\
  !*** ./components/SocialAndFeedback.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst SocialAndFeedback = ()=>{\n    const { language, t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const socialTexts = {\n        it: {\n            title: \"Seguici sui social\",\n            subtitle: \"Resta aggiornato sulle ultime novità e sfide\"\n        },\n        en: {\n            title: \"Follow us on social media\",\n            subtitle: \"Stay updated with the latest news and challenges\"\n        }\n    };\n    const socialTextContent = socialTexts[language] || socialTexts.it;\n    const socialLinks = [\n        {\n            name: 'Facebook',\n            url: 'https://facebook.com/fmchallenger',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined),\n            color: 'hover:text-blue-500'\n        },\n        {\n            name: 'Instagram',\n            url: 'https://instagram.com/fmchallenger',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined),\n            color: 'hover:text-pink-500'\n        },\n        {\n            name: 'X (Twitter)',\n            url: 'https://x.com/fmchallenger',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined),\n            color: 'hover:text-gray-400'\n        },\n        {\n            name: 'Reddit',\n            url: 'https://reddit.com/r/fmchallenger',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, undefined),\n            color: 'hover:text-orange-500'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid md:grid-cols-2 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-bgBox rounded-lg p-6 border border-textSecondary/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-textTitle mb-2\",\n                                children: socialTextContent.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-textSecondary text-sm\",\n                                children: socialTextContent.subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center gap-6\",\n                        children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: social.url,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: `text-textSecondary ${social.color} transition-colors duration-200 transform hover:scale-110`,\n                                \"aria-label\": `Follow us on ${social.name}`,\n                                children: social.icon\n                            }, social.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-bgBox rounded-lg p-6 border border-textSecondary/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-textTitle mb-2\",\n                                children: t.feedbackTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-textSecondary text-sm\",\n                                children: t.feedbackSubtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/feedback\",\n                            className: \"btn-primary inline-flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t.feedbackButton\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SocialAndFeedback.jsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocialAndFeedback);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SocialAndFeedback.jsx\n");

/***/ }),

/***/ "(ssr)/./components/Topbar.jsx":
/*!*******************************!*\
  !*** ./components/Topbar.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Topbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* harmony import */ var _LanguageSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LanguageSelector */ \"(ssr)/./components/LanguageSelector.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Topbar() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n            callbackUrl: '/'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-primary py-3 px-4 shadow-md border-b border-textSecondary/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex justify-between items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"flex items-center space-x-3 text-white font-bold text-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/logo/logo.png\",\n                            alt: \"FM Challenger Logo\",\n                            width: 32,\n                            height: 32,\n                            className: \"rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"FM Challenger\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        status === 'authenticated' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"flex items-center space-x-2 text-white\",\n                                    \"aria-expanded\": isMenuOpen,\n                                    \"aria-haspopup\": \"true\",\n                                    children: [\n                                        session.user.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: session.user.image,\n                                            alt: session.user.name || 'User',\n                                            width: 32,\n                                            height: 32,\n                                            className: \"rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white\",\n                                            children: session.user.name?.charAt(0) || 'U'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: session.user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 mt-2 w-48 bg-dropdownBg rounded-md shadow-lg py-1 z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile\",\n                                            className: \"block px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: t.profile\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile/saved-challenges\",\n                                            className: \"block px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: t.savedChallenges\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"block w-full text-left px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/signin\",\n                            className: \"text-white hover:text-gray-200 transition-colors\",\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Topbar.jsx\n");

/***/ }),

/***/ "(ssr)/./lib/LanguageContext.js":
/*!********************************!*\
  !*** ./lib/LanguageContext.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n// Definizione delle traduzioni\nconst translations = {\n    it: {\n        title: 'FM Challenger',\n        subtitle: 'Genera sfide random per Football Manager',\n        teamDifficulty: 'Difficoltà Campionato',\n        challengeDifficulty: 'Difficoltà Sfida',\n        select: 'Seleziona...',\n        generateChallenge: 'Genera Sfida 🎲',\n        regenerateChallenge: 'Rigenera Sfida 🔄',\n        shareChallenge: 'Condividi Sfida 📷',\n        loading: 'Generazione in corso... ⏳',\n        yourChallenge: 'La Tua Sfida',\n        team: 'Squadra',\n        challenges: 'Sfide',\n        objective: 'Obiettivo 🏆',\n        squad: 'Rosa 🏃',\n        tactics: 'Tattica ⚽',\n        footer: 'Creato con ❤️ per la community di Football Manager',\n        errorTeam: 'Non è stato possibile trovare una squadra con la difficoltà selezionata.',\n        errorChallenge: 'Non è stato possibile trovare sfide con la difficoltà selezionata.',\n        errorGeneric: 'Si è verificato un errore durante la generazione della sfida.',\n        filterByContinent: 'Filtra per continente:',\n        activeFilter: 'Filtro attivo: verranno selezionate solo squadre dai continenti scelti',\n        clickToLock: 'Clicca per bloccare',\n        locked: 'Bloccato',\n        aboutTitle: 'Cos\\'è FM Challenger?',\n        aboutDescription: 'FM Challenger è il generatore di sfide definitivo per Football Manager. Seleziona il livello di difficoltà per squadre e obiettivi, applica filtri per continente e genera sfide uniche per rendere le tue partite più emozionanti e imprevedibili.',\n        aboutFeatures: 'Caratteristiche principali: difficoltà personalizzabili, filtri geografici, sistema di blocco per rigenerazioni selettive, salvataggio sfide e supporto multilingua.',\n        welcomeTitle: 'Benvenuto in FM Challenger!',\n        welcomeSubtitle: 'Questa webapp ti permette di generare sfide casuali per Football Manager 2024. Ecco come funziona:',\n        teamDifficultyTitle: 'Difficoltà Campionato',\n        teamDifficultyDescription: 'Seleziona il livello di difficoltà del campionato. Questo determinerà quali squadre potranno essere selezionate per la tua sfida - dalle più blasonate alle più oscure.',\n        challengeDifficultyTitle: 'Difficoltà Sfide',\n        challengeDifficultyDescription: 'Scegli quanto vuoi rendere impegnative le sfide specifiche che dovrai completare. Da semplici obiettivi a missioni quasi impossibili!',\n        finalDescription: 'Una volta scelti questi parametri, premi \"Genera Sfida\" e il sistema ti proporrà una squadra casuale con tre sfide da completare: una per la rosa, una tattica e un obiettivo stagionale. Puoi generare quante sfide vuoi finché non trovi quella perfetta per te!',\n        feedbackTitle: 'Hai idee per nuove sfide?',\n        feedbackSubtitle: 'Lascia un feedback',\n        feedbackButton: 'Invia Feedback',\n        // Nuove traduzioni per le funzionalità\n        customizableDifficulties: 'Difficoltà Personalizzabili',\n        geographicalFilters: 'Filtri Geografici',\n        lockSystem: 'Sistema di Blocco',\n        multilingual: 'Multilingua',\n        generateChallengeToStart: 'Genera una sfida per iniziare!',\n        veryEasy: 'Molto Facile',\n        easy: 'Facile',\n        medium: 'Media',\n        hard: 'Difficile',\n        veryHard: 'Molto Difficile',\n        crazy: 'Matta',\n        meme: 'Meme',\n        profile: 'Profilo',\n        savedChallenges: 'Sfide Salvate',\n        activeChallenges: 'Sfide Attive',\n        completedChallenges: 'Sfide Completate',\n        archivedChallenges: 'Sfide Archiviate',\n        challengeCompleted: 'Sfida Completata',\n        archiveChallenge: 'Archivia',\n        unarchiveChallenge: 'Ripristina',\n        shareOnFacebook: 'Condividi su Facebook',\n        language: 'Lingua'\n    },\n    en: {\n        title: 'FM Challenger',\n        subtitle: 'Generate random challenges for Football Manager',\n        teamDifficulty: 'Team Difficulty',\n        challengeDifficulty: 'Challenge Difficulty',\n        select: 'Select...',\n        generateChallenge: 'Generate Challenge 🎲',\n        regenerateChallenge: 'Regenerate Challenge 🔄',\n        shareChallenge: 'Share Challenge 📷',\n        loading: 'Generating... ⏳',\n        yourChallenge: 'Your Challenge',\n        team: 'Team',\n        challenges: 'Challenges',\n        objective: 'Objective 🏆',\n        squad: 'Squad 🏃',\n        tactics: 'Tactics ⚽',\n        footer: 'Created with ❤️ for the Football Manager community',\n        errorTeam: 'Could not find a team with the selected difficulty.',\n        errorChallenge: 'Could not find challenges with the selected difficulty.',\n        errorGeneric: 'An error occurred while generating the challenge.',\n        filterByContinent: 'Filter by continent:',\n        activeFilter: 'Active filter: only teams from selected continents will be chosen',\n        clickToLock: 'Click to lock',\n        locked: 'Locked',\n        aboutTitle: 'What is FM Challenger?',\n        aboutDescription: 'FM Challenger is the ultimate challenge generator for Football Manager. Select difficulty levels for teams and objectives, apply continent filters, and generate unique challenges to make your games more exciting and unpredictable.',\n        aboutFeatures: 'Key features: customizable difficulties, geographical filters, lock system for selective regeneration, challenge saving, and multilingual support.',\n        welcomeTitle: 'Welcome to FM24 Challenge Creator!',\n        welcomeSubtitle: 'This webapp allows you to generate random challenges for Football Manager 2024. Here\\'s how it works:',\n        teamDifficultyTitle: 'Team Difficulty',\n        teamDifficultyDescription: 'Select the difficulty level of the league. This will determine which teams can be selected for your challenge - from the most prestigious to the most obscure.',\n        challengeDifficultyTitle: 'Challenge Difficulty',\n        challengeDifficultyDescription: 'Choose how challenging you want the specific challenges you need to complete to be. From simple objectives to nearly impossible missions!',\n        finalDescription: 'Once you\\'ve chosen these parameters, press \"Generate Challenge\" and the system will propose a random team with three challenges to complete: one for the squad, one tactical and one seasonal objective. You can generate as many challenges as you want until you find the perfect one for you!',\n        feedbackTitle: 'Have ideas for new challenges?',\n        feedbackSubtitle: 'Leave feedback',\n        feedbackButton: 'Send Feedback',\n        // New translations for features\n        customizableDifficulties: 'Customizable Difficulties',\n        geographicalFilters: 'Geographical Filters',\n        lockSystem: 'Lock System',\n        multilingual: 'Multilingual',\n        generateChallengeToStart: 'Generate a challenge to start!',\n        veryEasy: 'Very Easy',\n        easy: 'Easy',\n        medium: 'Medium',\n        hard: 'Hard',\n        veryHard: 'Very Hard',\n        crazy: 'Crazy',\n        meme: 'Meme',\n        profile: 'Profile',\n        savedChallenges: 'Saved Challenges',\n        activeChallenges: 'Active Challenges',\n        completedChallenges: 'Completed Challenges',\n        archivedChallenges: 'Archived Challenges',\n        challengeCompleted: 'Challenge Completed',\n        archiveChallenge: 'Archive',\n        unarchiveChallenge: 'Restore',\n        shareOnFacebook: 'Share on Facebook',\n        language: 'Language'\n    }\n};\n// Creazione del context\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"LanguageProvider.useState\": ()=>{\n            // Rileva la lingua del browser\n            if (false) {}\n            return 'en'; // Default fallback\n        }\n    }[\"LanguageProvider.useState\"]);\n    const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(translations.it);\n    // Rileva la lingua del browser all'avvio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const detectBrowserLanguage = {\n                \"LanguageProvider.useEffect.detectBrowserLanguage\": ()=>{\n                    if (false) {}\n                    return 'en';\n                }\n            }[\"LanguageProvider.useEffect.detectBrowserLanguage\"];\n            const detectedLang = detectBrowserLanguage();\n            setLanguage(detectedLang);\n            setT(translations[detectedLang]);\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Aggiorna le traduzioni quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            setT(translations[language]);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Funzione per cambiare lingua\n    const changeLanguage = (lang)=>{\n        if (translations[lang]) {\n            setLanguage(lang);\n            // Salva la preferenza dell'utente\n            if (false) {}\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            t,\n            changeLanguage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\lib\\\\LanguageContext.js\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n// Hook personalizzato per utilizzare il context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/LanguageContext.js\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.js":
/*!*************************!*\
  !*** ./lib/supabase.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChallengeDifficultyLevels: () => (/* binding */ getChallengeDifficultyLevels),\n/* harmony export */   getChallenges: () => (/* binding */ getChallenges),\n/* harmony export */   getRandomChallenge: () => (/* binding */ getRandomChallenge),\n/* harmony export */   getRandomTeam: () => (/* binding */ getRandomTeam),\n/* harmony export */   getTeamDifficultyLevels: () => (/* binding */ getTeamDifficultyLevels),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Ensure environment variables are available\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_API_URL || 'https://oahuzsvpjchoxtsxqgzn.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_API_ANON || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9haHV6c3ZwamNob3h0c3hxZ3puIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc3NjM5NzksImV4cCI6MjA2MzMzOTk3OX0.g7LEGCkOeno4gUkedzyL6BBKHSmvpzR8ok6APNq_hes';\n// Initialize the Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Function to get random teams based on difficulty and continents\nasync function getRandomTeam(difficulty, continents = []) {\n    // Inizia la query\n    let query = supabase.from('teams').select('id, name, league, country, logo, continent').eq('difficulty', difficulty);\n    // Aggiungi il filtro per i continenti se specificati\n    if (continents && continents.length > 0) {\n        query = query.in('continent', continents);\n    }\n    // Esegui la query\n    const { data, error } = await query.order('id', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching teams:', error);\n        return null;\n    }\n    // Select a random team from the results\n    if (data && data.length > 0) {\n        const randomIndex = Math.floor(Math.random() * data.length);\n        return data[randomIndex];\n    }\n    return null;\n}\n// Function to get random challenges based on difficulty and category\nasync function getRandomChallenge(difficulty, category, lang = 'it') {\n    const { data, error } = await supabase.from('challenges').select(`\n      id,\n      categoria,\n      difficolta,\n      translations(id, challenge_id, lang, text)\n    `).eq('difficolta', difficulty).eq('categoria', category);\n    if (error) {\n        console.error('Error fetching challenges:', error);\n        return null;\n    }\n    // Select a random challenge from the results\n    if (data && data.length > 0) {\n        const randomIndex = Math.floor(Math.random() * data.length);\n        const challenge = data[randomIndex];\n        // Find the translation in the requested language\n        const translation = challenge.translations.find((t)=>t.lang === lang);\n        return {\n            id: challenge.id,\n            category: challenge.categoria,\n            difficulty: challenge.difficolta,\n            text: translation ? translation.text : 'Translation not available'\n        };\n    }\n    return null;\n}\n// Function to get all challenges for a specific difficulty\nasync function getChallenges(difficulty, lang = 'it') {\n    // Use the correct category names from the database\n    const categories = [\n        'obiettivi',\n        'rosa',\n        'tattica'\n    ];\n    const challenges = {};\n    for (const category of categories){\n        const challenge = await getRandomChallenge(difficulty, category, lang);\n        if (challenge) {\n            // Use the category as the key in the challenges object\n            challenges[category] = challenge;\n        }\n    }\n    return challenges;\n}\n// Function to get team difficulty levels\nasync function getTeamDifficultyLevels(lang = 'it') {\n    const { data, error } = await supabase.from('difficulty_teams').select('*').eq('lang', lang).order('id', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching team difficulty levels:', error);\n        return [];\n    }\n    return data.map((level)=>({\n            value: level.team_difficulty_hook,\n            name: level.name,\n            description: level.difficulty_team_text\n        }));\n}\n// Function to get challenge difficulty levels\nasync function getChallengeDifficultyLevels(lang = 'it') {\n    const { data, error } = await supabase.from('difficulty_challenges').select('*').eq('lang', lang).order('id', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching challenge difficulty levels:', error);\n        return [];\n    }\n    return data.map((level)=>({\n            value: level.difficulty_challenge_hook,\n            name: level.name,\n            description: level.difficulty_challenge_text\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bf527086e705\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcUHJvZ2V0dGlcXGZtLWNoYWxsZW5nZXJcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZjUyNzA4NmU3MDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-roboto\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(rsc)/./lib/LanguageContext.js\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SessionProvider */ \"(rsc)/./components/SessionProvider.jsx\");\n/* harmony import */ var _components_Topbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Topbar */ \"(rsc)/./components/Topbar.jsx\");\n/* harmony import */ var _components_CookieBanner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/CookieBanner */ \"(rsc)/./components/CookieBanner.jsx\");\n/* harmony import */ var _components_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/GoogleAnalytics */ \"(rsc)/./components/GoogleAnalytics.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"FM Challenger - Create your challenge for FM\",\n    description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n    keywords: \"Football Manager, FM, challenge, random, generator, football, soccer, game, tactics, squad, objectives\",\n    authors: [\n        {\n            name: \"FM Challenger Team\"\n        }\n    ],\n    creator: \"FM Challenger\",\n    publisher: \"FM Challenger\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://fmchallenger.com'),\n    alternates: {\n        canonical: '/',\n        languages: {\n            'it-IT': '/it',\n            'en-US': '/en'\n        }\n    },\n    openGraph: {\n        title: \"FM Challenger - Create your challenge for FM\",\n        description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n        url: 'https://fmchallenger.com',\n        siteName: 'FM Challenger',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'FM Challenger - Football Manager Challenge Generator'\n            }\n        ],\n        locale: 'it_IT',\n        type: 'website'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: \"FM Challenger - Create your challenge for FM\",\n        description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n        images: [\n            '/og-image.jpg'\n        ],\n        creator: '@fmchallenger'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1A73E8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/img/favicon/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/img/favicon/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/img/favicon/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/img/favicon/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"FM Challenger\",\n                                \"description\": \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n                                \"url\": \"https://fmchallenger.com\",\n                                \"applicationCategory\": \"GameApplication\",\n                                \"operatingSystem\": \"Web\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"EUR\"\n                                },\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"FM Challenger Team\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8___default().variable)} font-roboto antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        GA_MEASUREMENT_ID: \"G-XXXXXXXXXX\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                                    position: \"top-center\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Topbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CookieBanner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./components/CookieBanner.jsx":
/*!*************************************!*\
  !*** ./components/CookieBanner.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\CookieBanner.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/GoogleAnalytics.jsx":
/*!****************************************!*\
  !*** ./components/GoogleAnalytics.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\GoogleAnalytics.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/SessionProvider.jsx":
/*!****************************************!*\
  !*** ./components/SessionProvider.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SessionProvider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\SessionProvider.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/Topbar.jsx":
/*!*******************************!*\
  !*** ./components/Topbar.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\Topbar.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./lib/LanguageContext.js":
/*!********************************!*\
  !*** ./lib/LanguageContext.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\lib\\LanguageContext.js",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\lib\\LanguageContext.js",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxQcm9nZXR0aVxcZm0tY2hhbGxlbmdlclxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions","vendor-chunks/html2canvas"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();