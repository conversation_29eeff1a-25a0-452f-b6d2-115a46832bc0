"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ShowChallenge.jsx":
/*!**************************************!*\
  !*** ./components/ShowChallenge.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShowChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _LockableItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LockableItem */ \"(app-pages-browser)/./components/LockableItem.jsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ShowChallenge(param) {\n    let { team, challenges, onRegenerate, initialLockedState } = param;\n    _s();\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const outputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveSuccess, setSaveSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Inizializza lo stato dei lock con i valori passati dal componente padre o con valori predefiniti\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.team) || false,\n        obiettivi: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.obiettivi) || false,\n        rosa: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.rosa) || false,\n        tattica: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.tattica) || false\n    });\n    // Aggiorna lo stato dei lock quando cambiano i valori iniziali\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShowChallenge.useEffect\": ()=>{\n            if (initialLockedState) {\n                setLockedItems(initialLockedState);\n            }\n        }\n    }[\"ShowChallenge.useEffect\"], [\n        initialLockedState\n    ]);\n    const toggleLock = (item)=>{\n        setLockedItems((prev)=>({\n                ...prev,\n                [item]: !prev[item]\n            }));\n    };\n    const handleRegenerate = ()=>{\n        // Passa lo stato corrente dei lock alla funzione di rigenerazione\n        onRegenerate(lockedItems);\n    };\n    const handleShare = async ()=>{\n        if (!outputRef.current) return;\n        try {\n            // Crea un canvas dall'elemento output\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(outputRef.current, {\n                backgroundColor: '#0D1117',\n                scale: 2,\n                logging: false,\n                useCORS: true\n            });\n            // Converti il canvas in un'immagine\n            const image = canvas.toDataURL('image/jpeg', 0.9);\n            // Crea un link per scaricare l'immagine\n            const link = document.createElement('a');\n            link.href = image;\n            // Crea il nome del file con data e ora\n            const date = new Date();\n            const formattedDate = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(date.getDate()).padStart(2, '0'), \"_\").concat(String(date.getHours()).padStart(2, '0'), \"-\").concat(String(date.getMinutes()).padStart(2, '0'));\n            link.download = \"FM-Challenger-\".concat(formattedDate, \".jpg\");\n            // Simula il click sul link per scaricare l'immagine\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error generating image:', error);\n        }\n    };\n    const handleSaveChallenge = async ()=>{\n        if (!session) return;\n        setSaving(true);\n        setSaveSuccess(false);\n        setSaveError('');\n        try {\n            const response = await fetch('/api/challenges/save', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    team,\n                    challenges\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Errore durante il salvataggio della sfida');\n            }\n            setSaveSuccess(true);\n            // Reset del messaggio di successo dopo 3 secondi\n            setTimeout(()=>{\n                setSaveSuccess(false);\n            }, 3000);\n        } catch (error) {\n            console.error('Error saving challenge:', error);\n            setSaveError(error.message || 'Errore durante il salvataggio della sfida');\n            // Reset del messaggio di errore dopo 3 secondi\n            setTimeout(()=>{\n                setSaveError('');\n            }, 3000);\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (!team || !challenges) {\n        return null;\n    }\n    // Aggiungi questo CSS per i bordi colorati\n    const challengeStyles = {\n        objective: {\n            titleColor: 'text-green-500',\n            borderColor: 'border-l-4 border-green-500'\n        },\n        squad: {\n            titleColor: 'text-yellow-500',\n            borderColor: 'border-l-4 border-yellow-500'\n        },\n        tactics: {\n            titleColor: 'text-red-500',\n            borderColor: 'border-l-4 border-red-500'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"output\",\n            ref: outputRef,\n            className: \"card mb-6 bg-opacity-90\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold mb-4 text-textTitle\",\n                    children: t.yourChallenge\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.team\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>toggleLock('team'),\n                            className: \"\\n              flex items-center space-x-4 p-3 rounded-md mb-2 cursor-pointer\\n              transition-all duration-200 ease-in-out\\n              \".concat(lockedItems.team ? 'border-2 border-accent bg-accent/10 shadow-md' : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80', \"\\n            \"),\n                            \"aria-label\": lockedItems.team ? \"Sblocca squadra\" : \"Blocca squadra\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-16 h-16 flex-shrink-0\",\n                                    children: team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: team.logo,\n                                        alt: \"Logo \".concat(team.name),\n                                        fill: true,\n                                        style: {\n                                            objectFit: 'contain'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-textTitle flex items-center\",\n                                            children: [\n                                                team.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-sm opacity-70\",\n                                                    children: lockedItems.team ? \"(\".concat(t.locked, \")\") : \"(\".concat(t.clickToLock, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textPrimary text-sm\",\n                                            children: [\n                                                team.country,\n                                                \" - \",\n                                                team.league\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 text-xl\",\n                                    children: lockedItems.team ? '🔒' : '🔓'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.challenges\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        challenges.obiettivi && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.objective.titleColor,\n                                children: t.objective\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 193,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.obiettivi.text,\n                            isLocked: lockedItems.obiettivi,\n                            onToggleLock: ()=>toggleLock('obiettivi'),\n                            className: \"mb-4 \".concat(challengeStyles.objective.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        challenges.rosa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.squad.titleColor,\n                                children: t.squad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.rosa.text,\n                            isLocked: lockedItems.rosa,\n                            onToggleLock: ()=>toggleLock('rosa'),\n                            className: \"mb-4 \".concat(challengeStyles.squad.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        challenges.tattica && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.tactics.titleColor,\n                                children: t.tactics\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.tattica.text,\n                            isLocked: lockedItems.tattica,\n                            onToggleLock: ()=>toggleLock('tattica'),\n                            className: \"mb-4 \".concat(challengeStyles.tactics.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                saveSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-green-900/20 border border-green-500/50 text-green-200 rounded-md\",\n                    children: \"Sfida salvata con successo!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this),\n                saveError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-900/20 border border-red-500/50 text-red-200 rounded-md\",\n                    children: saveError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex \".concat(session ? 'flex-col sm:flex-row' : 'flex-col sm:flex-row', \" gap-3\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRegenerate,\n                            className: \"btn-primary flex-1\",\n                            children: t.regenerateChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleShare,\n                            className: \"flex-1 bg-btnShare hover:bg-btnShareHover text-textPrimary py-3 px-6 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-glow-share transform hover:scale-105\",\n                            children: t.shareChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSaveChallenge,\n                            disabled: saving,\n                            className: \"btn-primary flex-1 bg-green-600 hover:bg-green-700\",\n                            children: [\n                                saving ? 'Salvataggio...' : 'Salva sfida',\n                                \" \\uD83D\\uDCBE\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(ShowChallenge, \"5MqpY5p4Hh2p6XcS/jrGftPjPtU=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession\n    ];\n});\n_c = ShowChallenge;\nvar _c;\n$RefreshReg$(_c, \"ShowChallenge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ShowChallenge.jsx\n"));

/***/ })

});