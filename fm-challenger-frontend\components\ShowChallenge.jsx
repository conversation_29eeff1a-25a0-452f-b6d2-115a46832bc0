'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import LockableItem from './LockableItem';
import { useLanguage } from '../lib/LanguageContext';
import html2canvas from 'html2canvas';
import { useSession } from 'next-auth/react';

export default function ShowChallenge({ team, challenges, onRegenerate, initialLockedState }) {
  const { t } = useLanguage();
  const { data: session } = useSession();
  const outputRef = useRef(null);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState('');

  // Inizializza lo stato dei lock con i valori passati dal componente padre o con valori predefiniti
  const [lockedItems, setLockedItems] = useState({
    team: initialLockedState?.team || false,
    obiettivi: initialLockedState?.obiettivi || false,
    rosa: initialLockedState?.rosa || false,
    tattica: initialLockedState?.tattica || false,
  });

  // Aggiorna lo stato dei lock quando cambiano i valori iniziali
  useEffect(() => {
    if (initialLockedState) {
      setLockedItems(initialLockedState);
    }
  }, [initialLockedState]);

  const toggleLock = (item) => {
    setLockedItems((prev) => ({
      ...prev,
      [item]: !prev[item],
    }));
  };

  const handleRegenerate = () => {
    // Passa lo stato corrente dei lock alla funzione di rigenerazione
    onRegenerate(lockedItems);
  };

  const handleShare = async () => {
    if (!outputRef.current) return;

    try {
      // Crea un canvas dall'elemento output
      const canvas = await html2canvas(outputRef.current, {
        backgroundColor: '#0D1117', // Sfondo uguale al tema
        scale: 2, // Migliore qualità
        logging: false,
        useCORS: true, // Permette di catturare immagini da domini esterni
      });

      // Converti il canvas in un'immagine
      const image = canvas.toDataURL('image/jpeg', 0.9);

      // Crea un link per scaricare l'immagine
      const link = document.createElement('a');
      link.href = image;

      // Crea il nome del file con data e ora
      const date = new Date();
      const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}_${String(date.getHours()).padStart(2, '0')}-${String(date.getMinutes()).padStart(2, '0')}`;
      link.download = `FM-Challenger-${formattedDate}.jpg`;

      // Simula il click sul link per scaricare l'immagine
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating image:', error);
    }
  };

  const handleSaveChallenge = async () => {
    if (!session) return;

    setSaving(true);
    setSaveSuccess(false);
    setSaveError('');

    try {
      const response = await fetch('/api/challenges/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          team,
          challenges,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore durante il salvataggio della sfida');
      }

      setSaveSuccess(true);

      // Reset del messaggio di successo dopo 3 secondi
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving challenge:', error);
      setSaveError(error.message || 'Errore durante il salvataggio della sfida');

      // Reset del messaggio di errore dopo 3 secondi
      setTimeout(() => {
        setSaveError('');
      }, 3000);
    } finally {
      setSaving(false);
    }
  };

  if (!team || !challenges) {
    return null;
  }

  // Aggiungi questo CSS per i bordi colorati
  const challengeStyles = {
    objective: {
      titleColor: 'text-green-500',
      borderColor: 'border-l-4 border-green-500'
    },
    squad: {
      titleColor: 'text-yellow-500',
      borderColor: 'border-l-4 border-yellow-500'
    },
    tactics: {
      titleColor: 'text-red-500',
      borderColor: 'border-l-4 border-red-500'
    }
  };

  return (
    <div className="w-full">
      <div id="output" ref={outputRef} className="card mb-6">
        <h2 className="text-xl font-semibold mb-4 text-textTitle">{t.yourChallenge}</h2>

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2 text-textTitle">{t.team}</h3>
          <div
            onClick={() => toggleLock('team')}
            className={`
              flex items-center space-x-4 p-3 rounded-md mb-2 cursor-pointer
              transition-all duration-200 ease-in-out
              ${lockedItems.team
                ? 'border-2 border-accent bg-accent/10 shadow-md'
                : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80'}
            `}
            aria-label={lockedItems.team ? "Sblocca squadra" : "Blocca squadra"}
          >
            <div className="relative w-16 h-16 flex-shrink-0">
              {team.logo && (
                <Image
                  src={team.logo}
                  alt={`Logo ${team.name}`}
                  fill
                  style={{ objectFit: 'contain' }}
                />
              )}
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-textTitle flex items-center">
                {team.name}
                <span className="ml-2 text-sm opacity-70">
                  {lockedItems.team ? `(${t.locked})` : `(${t.clickToLock})`}
                </span>
              </h4>
              <p className="text-textPrimary text-sm">
                {team.country} - {team.league}
              </p>
            </div>
            <div className="ml-4 text-xl">
              {lockedItems.team ? '🔒' : '🔓'}
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2 text-textTitle">{t.challenges}</h3>

          {challenges.obiettivi && (
            <LockableItem
              title={
                <span className={challengeStyles.objective.titleColor}>
                  {t.objective}
                </span>
              }
              content={challenges.obiettivi.text}
              isLocked={lockedItems.obiettivi}
              onToggleLock={() => toggleLock('obiettivi')}
              className={`mb-4 ${challengeStyles.objective.borderColor}`}
            />
          )}

          {challenges.rosa && (
            <LockableItem
              title={
                <span className={challengeStyles.squad.titleColor}>
                  {t.squad}
                </span>
              }
              content={challenges.rosa.text}
              isLocked={lockedItems.rosa}
              onToggleLock={() => toggleLock('rosa')}
              className={`mb-4 ${challengeStyles.squad.borderColor}`}
            />
          )}

          {challenges.tattica && (
            <LockableItem
              title={
                <span className={challengeStyles.tactics.titleColor}>
                  {t.tactics}
                </span>
              }
              content={challenges.tattica.text}
              isLocked={lockedItems.tattica}
              onToggleLock={() => toggleLock('tattica')}
              className={`mb-4 ${challengeStyles.tactics.borderColor}`}
            />
          )}
        </div>

        {/* Messaggi di feedback per il salvataggio */}
        {saveSuccess && (
          <div className="mb-4 p-3 bg-green-900/20 border border-green-500/50 text-green-200 rounded-md">
            Sfida salvata con successo!
          </div>
        )}

        {saveError && (
          <div className="mb-4 p-3 bg-red-900/20 border border-red-500/50 text-red-200 rounded-md">
            {saveError}
          </div>
        )}

        <div className={`flex ${session ? 'flex-col sm:flex-row' : 'flex-col sm:flex-row'} gap-3`}>
          <button onClick={handleRegenerate} className="btn-primary flex-1">
            {t.regenerateChallenge}
          </button>
          <button onClick={handleShare} className="flex-1 bg-btnShare hover:bg-btnShareHover text-textPrimary py-3 px-6 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-glow-share transform hover:scale-105">
            {t.shareChallenge}
          </button>

          {/* Pulsante Salva sfida visibile solo se l'utente è loggato */}
          {session && (
            <button
              onClick={handleSaveChallenge}
              disabled={saving}
              className="btn-primary flex-1 bg-green-600 hover:bg-green-700"
            >
              {saving ? 'Salvataggio...' : 'Salva sfida'} 💾
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

