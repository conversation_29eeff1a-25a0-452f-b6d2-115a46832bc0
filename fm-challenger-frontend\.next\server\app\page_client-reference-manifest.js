globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/CookieBanner.jsx":{"*":{"id":"(ssr)/./components/CookieBanner.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/GoogleAnalytics.jsx":{"*":{"id":"(ssr)/./components/GoogleAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/SessionProvider.jsx":{"*":{"id":"(ssr)/./components/SessionProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Topbar.jsx":{"*":{"id":"(ssr)/./components/Topbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/LanguageContext.js":{"*":{"id":"(ssr)/./lib/LanguageContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.js":{"*":{"id":"(ssr)/./app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.jsx":{"*":{"id":"(ssr)/./app/contact/page.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\CookieBanner.jsx":{"id":"(app-pages-browser)/./components/CookieBanner.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\GoogleAnalytics.jsx":{"id":"(app-pages-browser)/./components/GoogleAnalytics.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\SessionProvider.jsx":{"id":"(app-pages-browser)/./components/SessionProvider.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\Topbar.jsx":{"id":"(app-pages-browser)/./components/Topbar.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\lib\\LanguageContext.js":{"id":"(app-pages-browser)/./lib/LanguageContext.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\page.js":{"id":"(app-pages-browser)/./app/page.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\node_modules\\next\\dist\\esm\\lib\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\contact\\page.jsx":{"id":"(app-pages-browser)/./app/contact/page.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\":[],"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/CookieBanner.jsx":{"*":{"id":"(rsc)/./components/CookieBanner.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/GoogleAnalytics.jsx":{"*":{"id":"(rsc)/./components/GoogleAnalytics.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/SessionProvider.jsx":{"*":{"id":"(rsc)/./components/SessionProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Topbar.jsx":{"*":{"id":"(rsc)/./components/Topbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/LanguageContext.js":{"*":{"id":"(rsc)/./lib/LanguageContext.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.js":{"*":{"id":"(rsc)/./app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.jsx":{"*":{"id":"(rsc)/./app/contact/page.jsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}