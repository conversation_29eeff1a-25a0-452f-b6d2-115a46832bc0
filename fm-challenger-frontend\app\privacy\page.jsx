'use client';

import { useLanguage } from '../../lib/LanguageContext';

export default function PrivacyPage() {
  const { language } = useLanguage();

  const content = {
    it: {
      title: "Privacy Policy",
      lastUpdated: "Ultimo aggiornamento: ",
      sections: [
        {
          title: "1. Informazioni che raccogliamo",
          content: "Raccogliamo informazioni quando ti registri sul nostro sito, effettui il login o utilizzi i nostri servizi. Questo può includere il tuo nome, indirizzo email e preferenze di gioco."
        },
        {
          title: "2. Come utilizziamo le tue informazioni",
          content: "Utilizziamo le informazioni per fornire e migliorare i nostri servizi, personalizzare la tua esperienza e comunicare con te riguardo agli aggiornamenti del servizio."
        },
        {
          title: "3. Condivisione delle informazioni",
          content: "Non vendiamo, scambiamo o trasferiamo le tue informazioni personali a terze parti senza il tuo consenso, eccetto quando necessario per fornire i nostri servizi."
        },
        {
          title: "4. Cookie",
          content: "Utilizziamo cookie per migliorare la tua esperienza sul nostro sito. Puoi gestire le tue preferenze sui cookie attraverso il banner dei cookie."
        },
        {
          title: "5. Sicurezza",
          content: "Implementiamo misure di sicurezza appropriate per proteggere le tue informazioni personali contro accesso non autorizzato, alterazione, divulgazione o distruzione."
        }
      ]
    },
    en: {
      title: "Privacy Policy",
      lastUpdated: "Last updated: ",
      sections: [
        {
          title: "1. Information we collect",
          content: "We collect information when you register on our site, log in, or use our services. This may include your name, email address, and gaming preferences."
        },
        {
          title: "2. How we use your information",
          content: "We use information to provide and improve our services, personalize your experience, and communicate with you about service updates."
        },
        {
          title: "3. Information sharing",
          content: "We do not sell, trade, or transfer your personal information to third parties without your consent, except when necessary to provide our services."
        },
        {
          title: "4. Cookies",
          content: "We use cookies to enhance your experience on our site. You can manage your cookie preferences through the cookie banner."
        },
        {
          title: "5. Security",
          content: "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction."
        }
      ]
    }
  };

  const texts = content[language] || content.it;

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="card">
          <h1 className="text-3xl font-bold text-textTitle mb-4 gradient-text">
            {texts.title}
          </h1>
          
          <p className="text-textSecondary mb-8">
            {texts.lastUpdated}{new Date().toLocaleDateString(language === 'it' ? 'it-IT' : 'en-US')}
          </p>

          <div className="space-y-8">
            {texts.sections.map((section, index) => (
              <div key={index} className="border-l-4 border-accent pl-6">
                <h2 className="text-xl font-semibold text-textTitle mb-3">
                  {section.title}
                </h2>
                <p className="text-textSecondary leading-relaxed">
                  {section.content}
                </p>
              </div>
            ))}
          </div>

          <div className="mt-12 pt-8 border-t border-accent/20">
            <p className="text-textSecondary text-sm">
              {language === 'it' 
                ? 'Per domande riguardo questa privacy policy, contattaci a: <EMAIL>'
                : 'For questions about this privacy policy, contact us at: <EMAIL>'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
