"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/SelectChallenge.jsx":
/*!****************************************!*\
  !*** ./components/SelectChallenge.jsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SelectChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./lib/supabase.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SelectChallenge(param) {\n    let { onGenerateChallenge } = param;\n    _s();\n    const [teamDifficulty, setTeamDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [challengeDifficulty, setChallengeDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamDifficultyLevels, setTeamDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [challengeDifficultyLevels, setChallengeDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTeamDescription, setSelectedTeamDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChallengeDescription, setSelectedChallengeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedContinents, setSelectedContinents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    // Lista dei continenti disponibili\n    const continents = [\n        {\n            value: 'Europe',\n            label: 'Europa'\n        },\n        {\n            value: 'Asia',\n            label: 'Asia'\n        },\n        {\n            value: 'North America',\n            label: 'Nord America'\n        },\n        {\n            value: 'South America',\n            label: 'Sud America'\n        },\n        {\n            value: 'Africa',\n            label: 'Africa'\n        }\n    ];\n    // Carica i livelli di difficoltà dal database quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SelectChallenge.useEffect\": ()=>{\n            const fetchDifficultyLevels = {\n                \"SelectChallenge.useEffect.fetchDifficultyLevels\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Recupera i livelli di difficoltà per le squadre\n                        const teamLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getTeamDifficultyLevels)(language);\n                        setTeamDifficultyLevels(teamLevels);\n                        // Recupera i livelli di difficoltà per le sfide\n                        const challengeLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getChallengeDifficultyLevels)(language);\n                        setChallengeDifficultyLevels(challengeLevels);\n                        // Resetta le selezioni quando cambia la lingua\n                        setTeamDifficulty('');\n                        setChallengeDifficulty('');\n                        setSelectedTeamDescription('');\n                        setSelectedChallengeDescription('');\n                    } catch (error) {\n                        console.error('Error fetching difficulty levels:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SelectChallenge.useEffect.fetchDifficultyLevels\"];\n            fetchDifficultyLevels();\n        }\n    }[\"SelectChallenge.useEffect\"], [\n        language\n    ]);\n    // Aggiorna la descrizione quando cambia la difficoltà della squadra\n    const handleTeamDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setTeamDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = teamDifficultyLevels.find((level)=>level.value === value);\n        setSelectedTeamDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Aggiorna la descrizione quando cambia la difficoltà della sfida\n    const handleChallengeDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setChallengeDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = challengeDifficultyLevels.find((level)=>level.value === value);\n        setSelectedChallengeDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Gestisce il cambio di stato dei checkbox dei continenti\n    const handleContinentChange = (continent)=>{\n        setSelectedContinents((prev)=>{\n            if (prev.includes(continent)) {\n                return prev.filter((c)=>c !== continent);\n            } else {\n                return [\n                    ...prev,\n                    continent\n                ];\n            }\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (teamDifficulty && challengeDifficulty) {\n            onGenerateChallenge(teamDifficulty, challengeDifficulty, language, selectedContinents);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-4 text-textTitle\",\n                children: t.teamDifficulty\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: t.loading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"teamDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.teamDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"teamDifficulty\",\n                                value: teamDifficulty,\n                                onChange: handleTeamDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.teamDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    teamDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            selectedTeamDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedTeamDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"challengeDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.challengeDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"challengeDifficulty\",\n                                value: challengeDifficulty,\n                                onChange: handleChallengeDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.challengeDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    challengeDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            selectedChallengeDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedChallengeDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"block mb-2 text-textPrimary font-medium\",\n                                children: t.filterByContinent\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                                children: continents.map((continent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"continent-\".concat(continent.value),\n                                                checked: selectedContinents.includes(continent.value),\n                                                onChange: ()=>handleContinentChange(continent.value),\n                                                className: \"mr-2 h-4 w-4 accent-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"continent-\".concat(continent.value),\n                                                className: \"text-textPrimary text-sm\",\n                                                children: continent.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, continent.value, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            selectedContinents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-xs text-textSecondary\",\n                                children: t.activeFilter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"btn-primary w-full mt-6\",\n                        disabled: !teamDifficulty || !challengeDifficulty,\n                        children: t.generateChallenge\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(SelectChallenge, \"6RM6CGT09OcUtAEDpunkaBuVez8=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = SelectChallenge;\nvar _c;\n$RefreshReg$(_c, \"SelectChallenge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/SelectChallenge.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ShowChallenge.jsx":
/*!**************************************!*\
  !*** ./components/ShowChallenge.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShowChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _LockableItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LockableItem */ \"(app-pages-browser)/./components/LockableItem.jsx\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ShowChallenge(param) {\n    let { team, challenges, onRegenerate, initialLockedState } = param;\n    _s();\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const outputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveSuccess, setSaveSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Inizializza lo stato dei lock con i valori passati dal componente padre o con valori predefiniti\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.team) || false,\n        obiettivi: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.obiettivi) || false,\n        rosa: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.rosa) || false,\n        tattica: (initialLockedState === null || initialLockedState === void 0 ? void 0 : initialLockedState.tattica) || false\n    });\n    // Aggiorna lo stato dei lock quando cambiano i valori iniziali\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShowChallenge.useEffect\": ()=>{\n            if (initialLockedState) {\n                setLockedItems(initialLockedState);\n            }\n        }\n    }[\"ShowChallenge.useEffect\"], [\n        initialLockedState\n    ]);\n    const toggleLock = (item)=>{\n        setLockedItems((prev)=>({\n                ...prev,\n                [item]: !prev[item]\n            }));\n    };\n    const handleRegenerate = ()=>{\n        // Passa lo stato corrente dei lock alla funzione di rigenerazione\n        onRegenerate(lockedItems);\n    };\n    const handleShare = async ()=>{\n        if (!outputRef.current) return;\n        try {\n            // Crea un canvas dall'elemento output\n            const canvas = await html2canvas__WEBPACK_IMPORTED_MODULE_5___default()(outputRef.current, {\n                backgroundColor: '#0D1117',\n                scale: 2,\n                logging: false,\n                useCORS: true\n            });\n            // Converti il canvas in un'immagine\n            const image = canvas.toDataURL('image/jpeg', 0.9);\n            // Crea un link per scaricare l'immagine\n            const link = document.createElement('a');\n            link.href = image;\n            // Crea il nome del file con data e ora\n            const date = new Date();\n            const formattedDate = \"\".concat(date.getFullYear(), \"-\").concat(String(date.getMonth() + 1).padStart(2, '0'), \"-\").concat(String(date.getDate()).padStart(2, '0'), \"_\").concat(String(date.getHours()).padStart(2, '0'), \"-\").concat(String(date.getMinutes()).padStart(2, '0'));\n            link.download = \"FM-Challenger-\".concat(formattedDate, \".jpg\");\n            // Simula il click sul link per scaricare l'immagine\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error generating image:', error);\n        }\n    };\n    const handleSaveChallenge = async ()=>{\n        if (!session) return;\n        setSaving(true);\n        setSaveSuccess(false);\n        setSaveError('');\n        try {\n            const response = await fetch('/api/challenges/save', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    team,\n                    challenges\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Errore durante il salvataggio della sfida');\n            }\n            setSaveSuccess(true);\n            // Reset del messaggio di successo dopo 3 secondi\n            setTimeout(()=>{\n                setSaveSuccess(false);\n            }, 3000);\n        } catch (error) {\n            console.error('Error saving challenge:', error);\n            setSaveError(error.message || 'Errore durante il salvataggio della sfida');\n            // Reset del messaggio di errore dopo 3 secondi\n            setTimeout(()=>{\n                setSaveError('');\n            }, 3000);\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (!team || !challenges) {\n        return null;\n    }\n    // Aggiungi questo CSS per i bordi colorati\n    const challengeStyles = {\n        objective: {\n            titleColor: 'text-green-500',\n            borderColor: 'border-l-4 border-green-500'\n        },\n        squad: {\n            titleColor: 'text-yellow-500',\n            borderColor: 'border-l-4 border-yellow-500'\n        },\n        tactics: {\n            titleColor: 'text-red-500',\n            borderColor: 'border-l-4 border-red-500'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"output\",\n            ref: outputRef,\n            className: \"card mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold mb-4 text-textTitle\",\n                    children: t.yourChallenge\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.team\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>toggleLock('team'),\n                            className: \"\\n              flex items-center space-x-4 p-3 rounded-md mb-2 cursor-pointer\\n              transition-all duration-200 ease-in-out\\n              \".concat(lockedItems.team ? 'border-2 border-accent bg-accent/10 shadow-md' : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80', \"\\n            \"),\n                            \"aria-label\": lockedItems.team ? \"Sblocca squadra\" : \"Blocca squadra\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-16 h-16 flex-shrink-0\",\n                                    children: team.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: team.logo,\n                                        alt: \"Logo \".concat(team.name),\n                                        fill: true,\n                                        style: {\n                                            objectFit: 'contain'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-textTitle flex items-center\",\n                                            children: [\n                                                team.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-sm opacity-70\",\n                                                    children: lockedItems.team ? \"(\".concat(t.locked, \")\") : \"(\".concat(t.clickToLock, \")\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textPrimary text-sm\",\n                                            children: [\n                                                team.country,\n                                                \" - \",\n                                                team.league\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4 text-xl\",\n                                    children: lockedItems.team ? '🔒' : '🔓'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium mb-2 text-textTitle\",\n                            children: t.challenges\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        challenges.obiettivi && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.objective.titleColor,\n                                children: t.objective\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 193,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.obiettivi.text,\n                            isLocked: lockedItems.obiettivi,\n                            onToggleLock: ()=>toggleLock('obiettivi'),\n                            className: \"mb-4 \".concat(challengeStyles.objective.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        challenges.rosa && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.squad.titleColor,\n                                children: t.squad\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.rosa.text,\n                            isLocked: lockedItems.rosa,\n                            onToggleLock: ()=>toggleLock('rosa'),\n                            className: \"mb-4 \".concat(challengeStyles.squad.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        challenges.tattica && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LockableItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: challengeStyles.tactics.titleColor,\n                                children: t.tactics\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, void 0),\n                            content: challenges.tattica.text,\n                            isLocked: lockedItems.tattica,\n                            onToggleLock: ()=>toggleLock('tattica'),\n                            className: \"mb-4 \".concat(challengeStyles.tactics.borderColor)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                saveSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-green-900/20 border border-green-500/50 text-green-200 rounded-md\",\n                    children: \"Sfida salvata con successo!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this),\n                saveError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-900/20 border border-red-500/50 text-red-200 rounded-md\",\n                    children: saveError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex \".concat(session ? 'flex-col sm:flex-row' : 'flex-col sm:flex-row', \" gap-3\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRegenerate,\n                            className: \"btn-primary flex-1\",\n                            children: t.regenerateChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleShare,\n                            className: \"flex-1 bg-btnShare hover:bg-btnShareHover text-textPrimary py-3 px-6 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-glow-share transform hover:scale-105\",\n                            children: t.shareChallenge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSaveChallenge,\n                            disabled: saving,\n                            className: \"btn-primary flex-1 bg-green-600 hover:bg-green-700\",\n                            children: [\n                                saving ? 'Salvataggio...' : 'Salva sfida',\n                                \" \\uD83D\\uDCBE\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\ShowChallenge.jsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(ShowChallenge, \"5MqpY5p4Hh2p6XcS/jrGftPjPtU=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession\n    ];\n});\n_c = ShowChallenge;\nvar _c;\n$RefreshReg$(_c, \"ShowChallenge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ShowChallenge.jsx\n"));

/***/ })

});