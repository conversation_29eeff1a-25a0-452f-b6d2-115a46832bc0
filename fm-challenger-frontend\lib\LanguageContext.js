'use client';

import { createContext, useContext, useState, useEffect } from 'react';

// Definizione delle traduzioni
const translations = {
  it: {
    title: 'FM Challenger',
    subtitle: 'Genera sfide random per Football Manager',
    teamDifficulty: 'Difficoltà Campionato',
    challengeDifficulty: 'Difficoltà Sfida',
    select: 'Seleziona...',
    generateChallenge: 'Genera Sfida 🎲',
    regenerateChallenge: 'Rigenera Sfida 🔄',
    shareChallenge: 'Condividi Sfida 📷',
    loading: 'Generazione in corso... ⏳',
    yourChallenge: 'La Tua Sfida',
    team: '<PERSON>ra',
    challenges: 'Sfide',
    objective: 'Obiettivo 🏆',
    squad: 'Rosa 🏃',
    tactics: 'Tattica ⚽',
    footer: 'Creato con ❤️ per la community di Football Manager',
    errorTeam: 'Non è stato possibile trovare una squadra con la difficoltà selezionata.',
    errorChallenge: 'Non è stato possibile trovare sfide con la difficoltà selezionata.',
    errorGeneric: 'Si è verificato un errore durante la generazione della sfida.',
    filterByContinent: 'Filtra per continente:',
    activeFilter: 'Filtro attivo: verranno selezionate solo squadre dai continenti scelti',
    clickToLock: 'Clicca per bloccare',
    locked: 'Bloccato',
    aboutTitle: 'Cos\'è FM Challenger?',
    aboutDescription: 'FM Challenger è il generatore di sfide definitivo per Football Manager. Seleziona il livello di difficoltà per squadre e obiettivi, applica filtri per continente e genera sfide uniche per rendere le tue partite più emozionanti e imprevedibili.',
    aboutFeatures: 'Caratteristiche principali: difficoltà personalizzabili, filtri geografici, sistema di blocco per rigenerazioni selettive, salvataggio sfide e supporto multilingua.',
    welcomeTitle: 'Benvenuto in FM Challenger!',
    welcomeSubtitle: 'Questa webapp ti permette di generare sfide casuali per Football Manager 2024. Ecco come funziona:',
    teamDifficultyTitle: 'Difficoltà Campionato',
    teamDifficultyDescription: 'Seleziona il livello di difficoltà del campionato. Questo determinerà quali squadre potranno essere selezionate per la tua sfida - dalle più blasonate alle più oscure.',
    challengeDifficultyTitle: 'Difficoltà Sfide',
    challengeDifficultyDescription: 'Scegli quanto vuoi rendere impegnative le sfide specifiche che dovrai completare. Da semplici obiettivi a missioni quasi impossibili!',
    finalDescription: 'Una volta scelti questi parametri, premi "Genera Sfida" e il sistema ti proporrà una squadra casuale con tre sfide da completare: una per la rosa, una tattica e un obiettivo stagionale. Puoi generare quante sfide vuoi finché non trovi quella perfetta per te!',
    feedbackTitle: 'Hai idee per nuove sfide?',
    feedbackSubtitle: 'Lascia un feedback',
    feedbackButton: 'Invia Feedback',
    // Nuove traduzioni per le funzionalità
    customizableDifficulties: 'Difficoltà Personalizzabili',
    geographicalFilters: 'Filtri Geografici',
    lockSystem: 'Sistema di Blocco',
    multilingual: 'Multilingua',
    generateChallengeToStart: 'Genera una sfida per iniziare!',
    veryEasy: 'Molto Facile',
    easy: 'Facile',
    medium: 'Media',
    hard: 'Difficile',
    veryHard: 'Molto Difficile',
    crazy: 'Matta',
    meme: 'Meme',
    profile: 'Profilo',
    savedChallenges: 'Sfide Salvate',
    activeChallenges: 'Sfide Attive',
    completedChallenges: 'Sfide Completate',
    archivedChallenges: 'Sfide Archiviate',
    challengeCompleted: 'Sfida Completata',
    archiveChallenge: 'Archivia',
    unarchiveChallenge: 'Ripristina',
    shareOnFacebook: 'Condividi su Facebook',
    language: 'Lingua',
  },
  en: {
    title: 'FM Challenger',
    subtitle: 'Generate random challenges for Football Manager',
    teamDifficulty: 'Team Difficulty',
    challengeDifficulty: 'Challenge Difficulty',
    select: 'Select...',
    generateChallenge: 'Generate Challenge 🎲',
    regenerateChallenge: 'Regenerate Challenge 🔄',
    shareChallenge: 'Share Challenge 📷',
    loading: 'Generating... ⏳',
    yourChallenge: 'Your Challenge',
    team: 'Team',
    challenges: 'Challenges',
    objective: 'Objective 🏆',
    squad: 'Squad 🏃',
    tactics: 'Tactics ⚽',
    footer: 'Created with ❤️ for the Football Manager community',
    errorTeam: 'Could not find a team with the selected difficulty.',
    errorChallenge: 'Could not find challenges with the selected difficulty.',
    errorGeneric: 'An error occurred while generating the challenge.',
    filterByContinent: 'Filter by continent:',
    activeFilter: 'Active filter: only teams from selected continents will be chosen',
    clickToLock: 'Click to lock',
    locked: 'Locked',
    aboutTitle: 'What is FM Challenger?',
    aboutDescription: 'FM Challenger is the ultimate challenge generator for Football Manager. Select difficulty levels for teams and objectives, apply continent filters, and generate unique challenges to make your games more exciting and unpredictable.',
    aboutFeatures: 'Key features: customizable difficulties, geographical filters, lock system for selective regeneration, challenge saving, and multilingual support.',
    welcomeTitle: 'Welcome to FM24 Challenge Creator!',
    welcomeSubtitle: 'This webapp allows you to generate random challenges for Football Manager 2024. Here\'s how it works:',
    teamDifficultyTitle: 'Team Difficulty',
    teamDifficultyDescription: 'Select the difficulty level of the league. This will determine which teams can be selected for your challenge - from the most prestigious to the most obscure.',
    challengeDifficultyTitle: 'Challenge Difficulty',
    challengeDifficultyDescription: 'Choose how challenging you want the specific challenges you need to complete to be. From simple objectives to nearly impossible missions!',
    finalDescription: 'Once you\'ve chosen these parameters, press "Generate Challenge" and the system will propose a random team with three challenges to complete: one for the squad, one tactical and one seasonal objective. You can generate as many challenges as you want until you find the perfect one for you!',
    feedbackTitle: 'Have ideas for new challenges?',
    feedbackSubtitle: 'Leave feedback',
    feedbackButton: 'Send Feedback',
    // New translations for features
    customizableDifficulties: 'Customizable Difficulties',
    geographicalFilters: 'Geographical Filters',
    lockSystem: 'Lock System',
    multilingual: 'Multilingual',
    generateChallengeToStart: 'Generate a challenge to start!',
    veryEasy: 'Very Easy',
    easy: 'Easy',
    medium: 'Medium',
    hard: 'Hard',
    veryHard: 'Very Hard',
    crazy: 'Crazy',
    meme: 'Meme',
    profile: 'Profile',
    savedChallenges: 'Saved Challenges',
    activeChallenges: 'Active Challenges',
    completedChallenges: 'Completed Challenges',
    archivedChallenges: 'Archived Challenges',
    challengeCompleted: 'Challenge Completed',
    archiveChallenge: 'Archive',
    unarchiveChallenge: 'Restore',
    shareOnFacebook: 'Share on Facebook',
    language: 'Language',
  },
};

// Creazione del context
const LanguageContext = createContext();

export function LanguageProvider({ children }) {
  const [language, setLanguage] = useState(() => {
    // Rileva la lingua del browser
    if (typeof window !== 'undefined') {
      const browserLang = navigator.language || navigator.languages[0];
      const langCode = browserLang.split('-')[0]; // Prende solo il codice lingua (es: 'it' da 'it-IT')

      // Controlla se la lingua rilevata è supportata, altrimenti usa inglese
      return langCode === 'it' ? 'it' : 'en';
    }
    return 'en'; // Default fallback
  });
  const [t, setT] = useState(translations.it);

  // Rileva la lingua del browser all'avvio
  useEffect(() => {
    const detectBrowserLanguage = () => {
      if (typeof window !== 'undefined') {
        const browserLang = navigator.language.split('-')[0];
        return translations[browserLang] ? browserLang : 'en';
      }
      return 'en';
    };

    const detectedLang = detectBrowserLanguage();
    setLanguage(detectedLang);
    setT(translations[detectedLang]);
  }, []);

  // Aggiorna le traduzioni quando cambia la lingua
  useEffect(() => {
    setT(translations[language]);
  }, [language]);

  // Funzione per cambiare lingua
  const changeLanguage = (lang) => {
    if (translations[lang]) {
      setLanguage(lang);
      // Salva la preferenza dell'utente
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferredLanguage', lang);
      }
    }
  };

  return (
    <LanguageContext.Provider value={{ language, t, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook personalizzato per utilizzare il context
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

