'use client';

import { useState } from 'react';
import { useLanguage } from '../../lib/LanguageContext';

export default function ContactPage() {
  const { language } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const content = {
    it: {
      title: "Contattaci",
      subtitle: "Hai domande o suggerimenti? Siamo qui per aiutarti!",
      form: {
        name: "Nome",
        email: "Email",
        subject: "Ogg<PERSON>",
        message: "Messaggio",
        send: "Invia Messaggio",
        sending: "Invio in corso...",
        success: "Messaggio inviato con successo!",
        error: "Errore nell'invio del messaggio. Riprova."
      },
      info: {
        title: "Altre modalità di contatto",
        email: "Email: <EMAIL>",
        social: "Seguici sui social media per aggiornamenti e novità"
      }
    },
    en: {
      title: "Contact Us",
      subtitle: "Have questions or suggestions? We're here to help!",
      form: {
        name: "Name",
        email: "Email",
        subject: "Subject",
        message: "Message",
        send: "Send Message",
        sending: "Sending...",
        success: "Message sent successfully!",
        error: "Error sending message. Please try again."
      },
      info: {
        title: "Other ways to contact us",
        email: "Email: <EMAIL>",
        social: "Follow us on social media for updates and news"
      }
    }
  };

  const texts = content[language] || content.it;

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Qui implementeresti l'invio del form
    console.log('Form submitted:', formData);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-textTitle mb-4 gradient-text">
            {texts.title}
          </h1>
          <p className="text-textSecondary text-lg max-w-2xl mx-auto">
            {texts.subtitle}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="card">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-textTitle font-medium mb-2">
                  {texts.form.name}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-textTitle font-medium mb-2">
                  {texts.form.email}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-textTitle font-medium mb-2">
                  {texts.form.subject}
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-textTitle font-medium mb-2">
                  {texts.form.message}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="input-field w-full resize-none"
                />
              </div>

              <button
                type="submit"
                className="btn-primary w-full"
              >
                {texts.form.send}
              </button>
            </form>
          </div>

          {/* Contact Info */}
          <div className="space-y-8">
            <div className="card">
              <h3 className="text-xl font-semibold text-textTitle mb-4">
                {texts.info.title}
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <svg className="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                  <span className="text-textSecondary">{texts.info.email}</span>
                </div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-xl font-semibold text-textTitle mb-4">
                Social Media
              </h3>
              <p className="text-textSecondary mb-4">
                {texts.info.social}
              </p>
              
              <div className="flex space-x-4">
                {['Facebook', 'Instagram', 'X', 'Reddit'].map((social) => (
                  <a
                    key={social}
                    href={`#${social.toLowerCase()}`}
                    className="text-textSecondary hover:text-accent transition-colors"
                  >
                    {social}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
