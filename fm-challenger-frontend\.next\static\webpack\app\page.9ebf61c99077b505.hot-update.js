"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AboutSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AboutSection */ \"(app-pages-browser)/./components/AboutSection.jsx\");\n/* harmony import */ var _components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SelectChallenge */ \"(app-pages-browser)/./components/SelectChallenge.jsx\");\n/* harmony import */ var _components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ShowChallenge */ \"(app-pages-browser)/./components/ShowChallenge.jsx\");\n/* harmony import */ var _components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/SocialAndFeedback */ \"(app-pages-browser)/./components/SocialAndFeedback.jsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./lib/supabase.js\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__.useLanguage)();\n    const [team, setTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [challenges, setChallenges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDifficulties, setSelectedDifficulties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: '',\n        challenge: ''\n    });\n    // Funzione per tracciare eventi GA\n    const trackEvent = function(eventName) {\n        let eventParams = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if ( true && window.gtag) {\n            window.gtag('event', eventName, eventParams);\n        }\n    };\n    const generateChallenge = async function(teamDifficulty, challengeDifficulty) {\n        let lang = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'it', continents = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di generazione sfida\n        trackEvent('generate_challenge', {\n            team_difficulty: teamDifficulty,\n            challenge_difficulty: challengeDifficulty,\n            language: lang\n        });\n        // Salva le difficoltà selezionate per poterle riutilizzare durante la rigenerazione\n        setSelectedDifficulties({\n            team: teamDifficulty,\n            challenge: challengeDifficulty,\n            continents: continents\n        });\n        try {\n            console.log(\"Generating challenge with team difficulty: \".concat(teamDifficulty, \", challenge difficulty: \").concat(challengeDifficulty, \", language: \").concat(lang, \", continents: \").concat(continents.join(', ')));\n            // Get random team based on difficulty and continents\n            const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(teamDifficulty, continents);\n            // Get random challenges based on difficulty\n            const randomChallenges = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getChallenges)(challengeDifficulty, lang);\n            if (!randomTeam) {\n                throw new Error(t.errorTeam);\n            }\n            if (!randomChallenges || Object.keys(randomChallenges).length === 0) {\n                throw new Error(t.errorChallenge);\n            }\n            setTeam(randomTeam);\n            setChallenges(randomChallenges);\n        } catch (err) {\n            console.error('Error generating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stato per tenere traccia degli elementi bloccati\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: false,\n        obiettivi: false,\n        rosa: false,\n        tattica: false\n    });\n    const regenerateChallenge = async (currentLockedItems)=>{\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di rigenerazione sfida\n        trackEvent('regenerate_challenge', {\n            team_difficulty: selectedDifficulties.team,\n            challenge_difficulty: selectedDifficulties.challenge,\n            language: language\n        });\n        // Aggiorniamo lo stato dei lock con quello corrente\n        setLockedItems(currentLockedItems);\n        try {\n            let newTeam = team;\n            let newChallenges = {\n                ...challenges\n            };\n            // If team is not locked, get a new random team\n            if (!currentLockedItems.team && team) {\n                // Usa la difficoltà e i continenti salvati in precedenza\n                const continents = selectedDifficulties.continents || [];\n                const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(selectedDifficulties.team, continents);\n                if (randomTeam) {\n                    newTeam = randomTeam;\n                }\n            }\n            // For each challenge category that is not locked, get a new random challenge\n            for (const category of [\n                'obiettivi',\n                'rosa',\n                'tattica'\n            ]){\n                if (!currentLockedItems[category] && challenges && challenges[category]) {\n                    try {\n                        // Utilizziamo la funzione esistente per ottenere una nuova sfida casuale\n                        const newChallenge = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomChallenge)(selectedDifficulties.challenge, category, language);\n                        if (newChallenge && newChallenge.id !== challenges[category].id) {\n                            newChallenges[category] = newChallenge;\n                        }\n                    } catch (err) {\n                        console.error(\"Error regenerating challenge for category \".concat(category, \":\"), err);\n                    }\n                }\n            }\n            setTeam(newTeam);\n            setChallenges(newChallenges);\n        } catch (err) {\n            console.error('Error regenerating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const shareChallenge = async ()=>{\n        // Traccia l'evento di condivisione sfida\n        trackEvent('share_challenge', {\n            team_name: (team === null || team === void 0 ? void 0 : team.name) || 'unknown',\n            language: language\n        });\n    // Resto del codice esistente...\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-bgMain text-textPrimary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg p-6 md:p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8 \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg bg-cover bg-center bg-no-repeat p-6 md:p-8\",\n                    style: {\n                        backgroundImage: 'url(/img/image/sfondo.jpg)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 grid lg:grid-cols-2 gap-8 border-t border-textSecondary/10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onGenerateChallenge: generateChallenge\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-[400px] flex flex-col justify-center\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-textPrimary bg-bgBox\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-accent\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        t.loading\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"h-5 w-5 text-red-400\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            team && challenges && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                team: team,\n                                                challenges: challenges,\n                                                onRegenerate: regenerateChallenge,\n                                                initialLockedState: lockedItems\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            !team && !challenges && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-textSecondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1,\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg\",\n                                                            children: t.generateChallengeToStart\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 border-t border-textSecondary/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-secondary rounded-xl p-6 border-t border-textSecondary/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary text-sm\",\n                            children: [\n                                \"FM Challenger \\xa9 \",\n                                new Date().getFullYear(),\n                                \" - \",\n                                t.footer\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex justify-center space-x-4 text-xs text-textSecondary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/privacy\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Privacy Policy' : 'Privacy Policy'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/terms\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Termini di Servizio' : 'Terms of Service'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/contact\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Contatti' : 'Contact'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"4eWGB69s2Clp1cdwwlwDWBVteDA=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__.useLanguage\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});