import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import bcrypt from "bcryptjs";

const PLACEHOLDER_IMAGE = "https://ui-avatars.com/api/?background=0D8ABC&color=fff";

export async function POST(request) {
  try {
    const { username, email, password } = await request.json();

    // Validazione
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: "Username, email e password sono obbligatori" },
        { status: 400 }
      );
    }

    // Controlla se l'email è già in uso
    const { data: existingUser } = await supabase
      .from("app_users")
      .select("*")
      .eq("email", email)
      .single();

    if (existingUser) {
      return NextResponse.json(
        { error: "Email già in uso" },
        { status: 400 }
      );
    }

    // Cripta la password
    const hashedPassword = await bcrypt.hash(password, 10);

    // <PERSON>rea l'utente
    const { data, error } = await supabase
      .from("app_users")
      .insert([
        {
          username,
          email,
          password: hashedPassword,
          image: `${PLACEHOLDER_IMAGE}&name=${encodeURIComponent(username)}`,
          provider: "credentials",
          role: "utente"
        }
      ])
      .select();

    if (error) {
      console.error("Error creating user:", error);
      return NextResponse.json(
        { error: "Errore durante la registrazione" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: "Utente registrato con successo" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: "Errore durante la registrazione" },
      { status: 500 }
    );
  }
}
