import { createClient } from '@supabase/supabase-js';

// Ensure environment variables are available
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_API_URL || 'https://oahuzsvpjchoxtsxqgzn.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_API_ANON || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9haHV6c3ZwamNob3h0c3hxZ3puIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc3NjM5NzksImV4cCI6MjA2MzMzOTk3OX0.g7LEGCkOeno4gUkedzyL6BBKHSmvpzR8ok6APNq_hes';

// Initialize the Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function to get random teams based on difficulty and continents
export async function getRandomTeam(difficulty, continents = []) {
  // Inizia la query
  let query = supabase
    .from('teams')
    .select('id, name, league, country, logo, continent')
    .eq('difficulty', difficulty);

  // Aggiungi il filtro per i continenti se specificati
  if (continents && continents.length > 0) {
    query = query.in('continent', continents);
  }

  // Esegui la query
  const { data, error } = await query.order('id', { ascending: false });

  if (error) {
    console.error('Error fetching teams:', error);
    return null;
  }

  // Select a random team from the results
  if (data && data.length > 0) {
    const randomIndex = Math.floor(Math.random() * data.length);
    return data[randomIndex];
  }

  return null;
}

// Function to get random challenges based on difficulty and category
export async function getRandomChallenge(difficulty, category, lang = 'it') {
  const { data, error } = await supabase
    .from('challenges')
    .select(`
      id,
      categoria,
      difficolta,
      translations(id, challenge_id, lang, text)
    `)
    .eq('difficolta', difficulty)
    .eq('categoria', category);

  if (error) {
    console.error('Error fetching challenges:', error);
    return null;
  }

  // Select a random challenge from the results
  if (data && data.length > 0) {
    const randomIndex = Math.floor(Math.random() * data.length);
    const challenge = data[randomIndex];

    // Find the translation in the requested language
    const translation = challenge.translations.find(t => t.lang === lang);

    return {
      id: challenge.id,
      category: challenge.categoria,
      difficulty: challenge.difficolta,
      text: translation ? translation.text : 'Translation not available'
    };
  }

  return null;
}

// Function to get all challenges for a specific difficulty
export async function getChallenges(difficulty, lang = 'it') {
  // Use the correct category names from the database
  const categories = ['obiettivi', 'rosa', 'tattica'];
  const challenges = {};

  for (const category of categories) {
    const challenge = await getRandomChallenge(difficulty, category, lang);
    if (challenge) {
      // Use the category as the key in the challenges object
      challenges[category] = challenge;
    }
  }

  return challenges;
}

// Function to get team difficulty levels
export async function getTeamDifficultyLevels(lang = 'it') {
  const { data, error } = await supabase
    .from('difficulty_teams')
    .select('*')
    .eq('lang', lang)
    .order('id', { ascending: true });

  if (error) {
    console.error('Error fetching team difficulty levels:', error);
    return [];
  }

  return data.map(level => ({
    value: level.team_difficulty_hook,
    name: level.name,
    description: level.difficulty_team_text
  }));
}

// Function to get challenge difficulty levels
export async function getChallengeDifficultyLevels(lang = 'it') {
  const { data, error } = await supabase
    .from('difficulty_challenges')
    .select('*')
    .eq('lang', lang)
    .order('id', { ascending: true });

  if (error) {
    console.error('Error fetching challenge difficulty levels:', error);
    return [];
  }

  return data.map(level => ({
    value: level.difficulty_challenge_hook,
    name: level.name,
    description: level.difficulty_challenge_text
  }));
}
