/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/contact/page";
exports.ids = ["app/contact/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.jsx&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.jsx&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.jsx */ \"(rsc)/./app/contact/page.jsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'contact',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/contact/page\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.jsx&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.jsx */ \"(rsc)/./app/contact/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2NvbnRhY3QlNUMlNUNwYWdlLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXGFwcFxcXFxjb250YWN0XFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.jsx */ \"(ssr)/./app/contact/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2NvbnRhY3QlNUMlNUNwYWdlLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXGFwcFxcXFxjb250YWN0XFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/CookieBanner.jsx */ \"(rsc)/./components/CookieBanner.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GoogleAnalytics.jsx */ \"(rsc)/./components/GoogleAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SessionProvider.jsx */ \"(rsc)/./components/SessionProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Topbar.jsx */ \"(rsc)/./components/Topbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/LanguageContext.js */ \"(rsc)/./lib/LanguageContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/CookieBanner.jsx */ \"(ssr)/./components/CookieBanner.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/GoogleAnalytics.jsx */ \"(ssr)/./components/GoogleAnalytics.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/SessionProvider.jsx */ \"(ssr)/./components/SessionProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Topbar.jsx */ \"(ssr)/./components/Topbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/LanguageContext.js */ \"(ssr)/./lib/LanguageContext.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDb29raWVCYW5uZXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN1c2VyJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpJTVDJTVDZm0tY2hhbGxlbmdlciU1QyU1Q2ZtLWNoYWxsZW5nZXItZnJvbnRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDR29vZ2xlQW5hbHl0aWNzLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDdXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNQcm9nZXR0aSU1QyU1Q2ZtLWNoYWxsZW5nZXIlNUMlNUNmbS1jaGFsbGVuZ2VyLWZyb250ZW5kJTVDJTVDY29tcG9uZW50cyU1QyU1Q1Nlc3Npb25Qcm92aWRlci5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUb3BiYXIuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN1c2VyJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpJTVDJTVDZm0tY2hhbGxlbmdlciU1QyU1Q2ZtLWNoYWxsZW5nZXItZnJvbnRlbmQlNUMlNUNsaWIlNUMlNUNMYW5ndWFnZUNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJMYW5ndWFnZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQuanMlNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJSb2JvdG8lNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1yb2JvdG8lNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ3ZWlnaHQlNUMlMjIlM0ElNUIlNUMlMjIzMDAlNUMlMjIlMkMlNUMlMjI0MDAlNUMlMjIlMkMlNUMlMjI1MDAlNUMlMjIlMkMlNUMlMjI3MDAlNUMlMjIlNUQlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMnJvYm90byU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN1c2VyJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpJTVDJTVDZm0tY2hhbGxlbmdlciU1QyU1Q2ZtLWNoYWxsZW5nZXItZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNyZWFjdC1ob3QtdG9hc3QlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXlLO0FBQ3pLO0FBQ0EsNEtBQTRLO0FBQzVLO0FBQ0EsNEtBQTRLO0FBQzVLO0FBQ0EsMEpBQW1LO0FBQ25LO0FBQ0EsNEpBQTZLO0FBQzdLO0FBQ0Esc01BQTJMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXENvb2tpZUJhbm5lci5qc3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcR29vZ2xlQW5hbHl0aWNzLmpzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRGVza3RvcFxcXFxQcm9nZXR0aVxcXFxmbS1jaGFsbGVuZ2VyXFxcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXFxcY29tcG9uZW50c1xcXFxTZXNzaW9uUHJvdmlkZXIuanN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXFRvcGJhci5qc3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkxhbmd1YWdlUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXGxpYlxcXFxMYW5ndWFnZUNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxyZWFjdC1ob3QtdG9hc3RcXFxcZGlzdFxcXFxpbmRleC5tanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CCookieBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CGoogleAnalytics.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Ccomponents%5C%5CTopbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Clib%5C%5CLanguageContext.js%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Roboto%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-roboto%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22roboto%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDdXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNQcm9nZXR0aSU1QyU1Q2ZtLWNoYWxsZW5nZXIlNUMlNUNmbS1jaGFsbGVuZ2VyLWZyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN1c2VyJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2dldHRpJTVDJTVDZm0tY2hhbGxlbmdlciU1QyU1Q2ZtLWNoYWxsZW5nZXItZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3VzZXIlNUMlNUNEZXNrdG9wJTVDJTVDUHJvZ2V0dGklNUMlNUNmbS1jaGFsbGVuZ2VyJTVDJTVDZm0tY2hhbGxlbmdlci1mcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDbGliJTVDJTVDbWV0YWRhdGElNUMlNUNtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTRLO0FBQzVLO0FBQ0EsME9BQStLO0FBQy9LO0FBQ0EsME9BQStLO0FBQy9LO0FBQ0Esb1JBQXFNO0FBQ3JNO0FBQ0Esd09BQThLO0FBQzlLO0FBQ0Esc1FBQTZMO0FBQzdMO0FBQ0Esc09BQTZLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRGVza3RvcFxcXFxQcm9nZXR0aVxcXFxmbS1jaGFsbGVuZ2VyXFxcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRGVza3RvcFxcXFxQcm9nZXR0aVxcXFxmbS1jaGFsbGVuZ2VyXFxcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEZXNrdG9wXFxcXFByb2dldHRpXFxcXGZtLWNoYWxsZW5nZXJcXFxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGxpYlxcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/contact/page.jsx":
/*!******************************!*\
  !*** ./app/contact/page.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ContactPage() {\n    const { language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n    });\n    const content = {\n        it: {\n            title: \"Contattaci\",\n            subtitle: \"Hai domande o suggerimenti? Siamo qui per aiutarti!\",\n            form: {\n                name: \"Nome\",\n                email: \"Email\",\n                subject: \"Oggetto\",\n                message: \"Messaggio\",\n                send: \"Invia Messaggio\",\n                sending: \"Invio in corso...\",\n                success: \"Messaggio inviato con successo!\",\n                error: \"Errore nell'invio del messaggio. Riprova.\"\n            },\n            info: {\n                title: \"Altre modalità di contatto\",\n                email: \"Email: <EMAIL>\",\n                social: \"Seguici sui social media per aggiornamenti e novità\"\n            }\n        },\n        en: {\n            title: \"Contact Us\",\n            subtitle: \"Have questions or suggestions? We're here to help!\",\n            form: {\n                name: \"Name\",\n                email: \"Email\",\n                subject: \"Subject\",\n                message: \"Message\",\n                send: \"Send Message\",\n                sending: \"Sending...\",\n                success: \"Message sent successfully!\",\n                error: \"Error sending message. Please try again.\"\n            },\n            info: {\n                title: \"Other ways to contact us\",\n                email: \"Email: <EMAIL>\",\n                social: \"Follow us on social media for updates and news\"\n            }\n        }\n    };\n    const texts = content[language] || content.it;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Qui implementeresti l'invio del form\n        console.log('Form submitted:', formData);\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-bgMain text-textPrimary py-12 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-textTitle mb-4 gradient-text\",\n                            children: texts.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary text-lg max-w-2xl mx-auto\",\n                            children: texts.subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"subject\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"subject\",\n                                                name: \"subject\",\n                                                value: formData.subject,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                required: true,\n                                                rows: 6,\n                                                className: \"input-field w-full resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary w-full\",\n                                        children: texts.form.send\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-textTitle mb-4\",\n                                            children: texts.info.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-accent\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-textSecondary\",\n                                                        children: texts.info.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-textTitle mb-4\",\n                                            children: \"Social Media\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-4\",\n                                            children: texts.info.social\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                'Facebook',\n                                                'Instagram',\n                                                'X',\n                                                'Reddit'\n                                            ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `#${social.toLowerCase()}`,\n                                                    className: \"text-textSecondary hover:text-accent transition-colors\",\n                                                    children: social\n                                                }, social, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contact/page.jsx\n");

/***/ }),

/***/ "(ssr)/./components/CookieBanner.jsx":
/*!*************************************!*\
  !*** ./components/CookieBanner.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst CookieBanner = ()=>{\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to clear corrupted data\n    const clearCookieData = ()=>{\n        if (false) {}\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieBanner.useEffect\": ()=>{\n            // Check if localStorage is available and user has already made a choice\n            if (false) {} else {\n                // If localStorage is not available, show banner\n                setShowBanner(true);\n            }\n        }\n    }[\"CookieBanner.useEffect\"], []);\n    const updateGoogleConsent = (consentData)=>{\n        if (false) {}\n    };\n    const saveConsent = (consent)=>{\n        try {\n            if (false) {}\n        } catch (error) {\n            console.error('Error saving cookie consent:', error);\n        }\n        return false;\n    };\n    const handleAcceptAll = ()=>{\n        const consent = {\n            analytics: true,\n            advertising: true,\n            functional: true,\n            timestamp: Date.now()\n        };\n        saveConsent(consent);\n    };\n    const handleRejectAll = ()=>{\n        const consent = {\n            analytics: false,\n            advertising: false,\n            functional: true,\n            timestamp: Date.now()\n        };\n        saveConsent(consent);\n    };\n    const handleCustomize = (customConsent)=>{\n        const consent = {\n            ...customConsent,\n            functional: true,\n            timestamp: Date.now()\n        };\n        if (saveConsent(consent)) {\n            setShowDetails(false);\n        }\n    };\n    if (!showBanner) return null;\n    const cookieTexts = {\n        it: {\n            title: \"Utilizziamo i cookie\",\n            description: \"Utilizziamo cookie e tecnologie simili per migliorare la tua esperienza, analizzare il traffico e personalizzare i contenuti. Puoi scegliere quali cookie accettare.\",\n            acceptAll: \"Accetta tutti\",\n            rejectAll: \"Rifiuta tutti\",\n            customize: \"Personalizza\",\n            save: \"Salva preferenze\",\n            necessary: \"Cookie necessari\",\n            necessaryDesc: \"Essenziali per il funzionamento del sito\",\n            analytics: \"Cookie analitici\",\n            analyticsDesc: \"Ci aiutano a capire come utilizzi il sito\",\n            advertising: \"Cookie pubblicitari\",\n            advertisingDesc: \"Utilizzati per mostrare annunci pertinenti\"\n        },\n        en: {\n            title: \"We use cookies\",\n            description: \"We use cookies and similar technologies to improve your experience, analyze traffic and personalize content. You can choose which cookies to accept.\",\n            acceptAll: \"Accept all\",\n            rejectAll: \"Reject all\",\n            customize: \"Customize\",\n            save: \"Save preferences\",\n            necessary: \"Necessary cookies\",\n            necessaryDesc: \"Essential for the website to function\",\n            analytics: \"Analytics cookies\",\n            analyticsDesc: \"Help us understand how you use the site\",\n            advertising: \"Advertising cookies\",\n            advertisingDesc: \"Used to show relevant ads\"\n        }\n    };\n    const texts = cookieTexts[language] || cookieTexts.it;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-0 left-0 right-0 bg-bgBox border-t border-textSecondary/20 p-4 md:p-6 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: !showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row items-start md:items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-textPrimary mb-2\",\n                                        children: texts.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-textSecondary text-sm\",\n                                        children: texts.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-2 w-full md:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRejectAll,\n                                        className: \"px-4 py-2 text-textSecondary border border-textSecondary/30 rounded-md hover:bg-textSecondary/10 transition-colors\",\n                                        children: texts.rejectAll\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowDetails(true),\n                                        className: \"px-4 py-2 text-accent border border-accent rounded-md hover:bg-accent/10 transition-colors\",\n                                        children: texts.customize\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAcceptAll,\n                                        className: \"px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors\",\n                                        children: texts.acceptAll\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CookieDetails, {\n                        texts: texts,\n                        onSave: handleCustomize,\n                        onBack: ()=>setShowDetails(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 202,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst CookieDetails = ({ texts, onSave, onBack })=>{\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        analytics: false,\n        advertising: false\n    });\n    const handleSave = ()=>{\n        onSave(preferences);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-textPrimary\",\n                        children: texts.customize\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"text-textSecondary hover:text-textPrimary\",\n                        children: \"←\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.necessary\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.necessaryDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-green-500 font-medium\",\n                                children: \"ON\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.analytics\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.analyticsDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"relative inline-flex items-center cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: preferences.analytics,\n                                        onChange: (e)=>setPreferences((prev)=>({\n                                                    ...prev,\n                                                    analytics: e.target.checked\n                                                })),\n                                        className: \"sr-only peer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-bgMain rounded-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-textPrimary\",\n                                        children: texts.advertising\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-textSecondary\",\n                                        children: texts.advertisingDesc\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"relative inline-flex items-center cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: preferences.advertising,\n                                        onChange: (e)=>setPreferences((prev)=>({\n                                                    ...prev,\n                                                    advertising: e.target.checked\n                                                })),\n                                        className: \"sr-only peer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 pt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleSave,\n                    className: \"flex-1 px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors\",\n                    children: texts.save\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieBanner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/CookieBanner.jsx\n");

/***/ }),

/***/ "(ssr)/./components/GoogleAnalytics.jsx":
/*!****************************************!*\
  !*** ./components/GoogleAnalytics.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoogleAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction GoogleAnalyticsInner({ GA_MEASUREMENT_ID }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"GoogleAnalyticsInner.useEffect\": ()=>{\n            if (pathname && window.gtag) {\n                window.gtag('config', GA_MEASUREMENT_ID, {\n                    page_path: pathname\n                });\n            }\n        }\n    }[\"GoogleAnalyticsInner.useEffect\"], [\n        pathname,\n        searchParams,\n        GA_MEASUREMENT_ID\n    ]);\n    return null;\n}\nfunction GoogleAnalytics({ GA_MEASUREMENT_ID }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                strategy: \"afterInteractive\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics-consent\",\n                strategy: \"beforeInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n\n            // Initialize Google Consent Mode v2\n            gtag('consent', 'default', {\n              'analytics_storage': 'denied',\n              'ad_storage': 'denied',\n              'ad_user_data': 'denied',\n              'ad_personalization': 'denied',\n              'functionality_storage': 'granted',\n              'security_storage': 'granted'\n            });\n\n            gtag('js', new Date());\n          `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics\",\n                strategy: \"afterInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n            gtag('config', '${GA_MEASUREMENT_ID}', {\n              page_path: window.location.pathname,\n            });\n          `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_3__.Suspense, {\n                fallback: null,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleAnalyticsInner, {\n                    GA_MEASUREMENT_ID: GA_MEASUREMENT_ID\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/GoogleAnalytics.jsx\n");

/***/ }),

/***/ "(ssr)/./components/LanguageSelector.jsx":
/*!*****************************************!*\
  !*** ./components/LanguageSelector.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LanguageSelector() {\n    const { language, changeLanguage } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'it',\n            name: 'Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'en',\n            name: 'English',\n            flag: '🇬🇧'\n        }\n    ];\n    const currentLanguage = languages.find((lang)=>lang.code === language);\n    const handleLanguageChange = (langCode)=>{\n        changeLanguage(langCode);\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 rounded-md bg-dropdownBg text-textPrimary hover:bg-bgBox transition-colors\",\n                \"aria-label\": \"Select language\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentLanguage?.flag\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: currentLanguage?.code.toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-40 bg-dropdownBg rounded-md shadow-lg py-1 z-10 border border-textSecondary/20\",\n                children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleLanguageChange(lang.code),\n                        className: `w-full text-left px-4 py-2 text-sm flex items-center space-x-3 hover:bg-bgBox transition-colors ${language === lang.code ? 'bg-accent/20 text-accent' : 'text-textPrimary'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg\",\n                                children: lang.flag\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: lang.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, lang.code, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\LanguageSelector.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LanguageSelector.jsx\n");

/***/ }),

/***/ "(ssr)/./components/SessionProvider.jsx":
/*!****************************************!*\
  !*** ./components/SessionProvider.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SessionProvider.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTZFO0FBRTlELFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQUVDLE9BQU8sRUFBRTtJQUMzRCxxQkFDRSw4REFBQ0YsNERBQXVCQTtRQUFDRSxTQUFTQTtrQkFDL0JEOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxQcm9nZXR0aVxcZm0tY2hhbGxlbmdlclxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcY29tcG9uZW50c1xcU2Vzc2lvblByb3ZpZGVyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2Vzc2lvblByb3ZpZGVyKHsgY2hpbGRyZW4sIHNlc3Npb24gfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgc2Vzc2lvbj17c2Vzc2lvbn0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNlc3Npb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./components/Topbar.jsx":
/*!*******************************!*\
  !*** ./components/Topbar.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Topbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/LanguageContext */ \"(ssr)/./lib/LanguageContext.js\");\n/* harmony import */ var _LanguageSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LanguageSelector */ \"(ssr)/./components/LanguageSelector.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Topbar() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const { t } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    const handleSignOut = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n            callbackUrl: '/'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-primary py-3 px-4 shadow-md border-b border-textSecondary/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex justify-between items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"flex items-center space-x-3 text-white font-bold text-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/img/logo/logo.png\",\n                            alt: \"FM Challenger Logo\",\n                            width: 32,\n                            height: 32,\n                            className: \"rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"FM Challenger\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        status === 'authenticated' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"flex items-center space-x-2 text-white\",\n                                    \"aria-expanded\": isMenuOpen,\n                                    \"aria-haspopup\": \"true\",\n                                    children: [\n                                        session.user.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: session.user.image,\n                                            alt: session.user.name || 'User',\n                                            width: 32,\n                                            height: 32,\n                                            className: \"rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white\",\n                                            children: session.user.name?.charAt(0) || 'U'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: session.user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 mt-2 w-48 bg-dropdownBg rounded-md shadow-lg py-1 z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile\",\n                                            className: \"block px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: t.profile\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile/saved-challenges\",\n                                            className: \"block px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: t.savedChallenges\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"block w-full text-left px-4 py-2 text-sm text-textPrimary hover:bg-bgBox\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/signin\",\n                            className: \"text-white hover:text-gray-200 transition-colors\",\n                            children: \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Topbar.jsx\n");

/***/ }),

/***/ "(ssr)/./lib/LanguageContext.js":
/*!********************************!*\
  !*** ./lib/LanguageContext.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n// Definizione delle traduzioni\nconst translations = {\n    it: {\n        title: 'FM Challenger',\n        subtitle: 'Genera sfide random per Football Manager',\n        teamDifficulty: 'Difficoltà Campionato',\n        challengeDifficulty: 'Difficoltà Sfida',\n        select: 'Seleziona...',\n        generateChallenge: 'Genera Sfida 🎲',\n        regenerateChallenge: 'Rigenera Sfida 🔄',\n        shareChallenge: 'Condividi Sfida 📷',\n        loading: 'Generazione in corso... ⏳',\n        yourChallenge: 'La Tua Sfida',\n        team: 'Squadra',\n        challenges: 'Sfide',\n        objective: 'Obiettivo 🏆',\n        squad: 'Rosa 🏃',\n        tactics: 'Tattica ⚽',\n        footer: 'Creato con ❤️ per la community di Football Manager',\n        errorTeam: 'Non è stato possibile trovare una squadra con la difficoltà selezionata.',\n        errorChallenge: 'Non è stato possibile trovare sfide con la difficoltà selezionata.',\n        errorGeneric: 'Si è verificato un errore durante la generazione della sfida.',\n        filterByContinent: 'Filtra per continente:',\n        activeFilter: 'Filtro attivo: verranno selezionate solo squadre dai continenti scelti',\n        clickToLock: 'Clicca per bloccare',\n        locked: 'Bloccato',\n        aboutTitle: 'Cos\\'è FM Challenger?',\n        aboutDescription: 'FM Challenger è il generatore di sfide definitivo per Football Manager. Seleziona il livello di difficoltà per squadre e obiettivi, applica filtri per continente e genera sfide uniche per rendere le tue partite più emozionanti e imprevedibili.',\n        aboutFeatures: 'Caratteristiche principali: difficoltà personalizzabili, filtri geografici, sistema di blocco per rigenerazioni selettive, salvataggio sfide e supporto multilingua.',\n        welcomeTitle: 'Benvenuto in FM Challenger!',\n        welcomeSubtitle: 'Questa webapp ti permette di generare sfide casuali per Football Manager 2024. Ecco come funziona:',\n        teamDifficultyTitle: 'Difficoltà Campionato',\n        teamDifficultyDescription: 'Seleziona il livello di difficoltà del campionato. Questo determinerà quali squadre potranno essere selezionate per la tua sfida - dalle più blasonate alle più oscure.',\n        challengeDifficultyTitle: 'Difficoltà Sfide',\n        challengeDifficultyDescription: 'Scegli quanto vuoi rendere impegnative le sfide specifiche che dovrai completare. Da semplici obiettivi a missioni quasi impossibili!',\n        finalDescription: 'Una volta scelti questi parametri, premi \"Genera Sfida\" e il sistema ti proporrà una squadra casuale con tre sfide da completare: una per la rosa, una tattica e un obiettivo stagionale. Puoi generare quante sfide vuoi finché non trovi quella perfetta per te!',\n        feedbackTitle: 'Hai idee per nuove sfide?',\n        feedbackSubtitle: 'Lascia un feedback',\n        feedbackButton: 'Invia Feedback',\n        // Nuove traduzioni per le funzionalità\n        customizableDifficulties: 'Difficoltà Personalizzabili',\n        geographicalFilters: 'Filtri Geografici',\n        lockSystem: 'Sistema di Blocco',\n        multilingual: 'Multilingua',\n        generateChallengeToStart: 'Genera una sfida per iniziare!',\n        veryEasy: 'Molto Facile',\n        easy: 'Facile',\n        medium: 'Media',\n        hard: 'Difficile',\n        veryHard: 'Molto Difficile',\n        crazy: 'Matta',\n        meme: 'Meme',\n        profile: 'Profilo',\n        savedChallenges: 'Sfide Salvate',\n        activeChallenges: 'Sfide Attive',\n        completedChallenges: 'Sfide Completate',\n        archivedChallenges: 'Sfide Archiviate',\n        challengeCompleted: 'Sfida Completata',\n        archiveChallenge: 'Archivia',\n        unarchiveChallenge: 'Ripristina',\n        shareOnFacebook: 'Condividi su Facebook',\n        language: 'Lingua'\n    },\n    en: {\n        title: 'FM Challenger',\n        subtitle: 'Generate random challenges for Football Manager',\n        teamDifficulty: 'Team Difficulty',\n        challengeDifficulty: 'Challenge Difficulty',\n        select: 'Select...',\n        generateChallenge: 'Generate Challenge 🎲',\n        regenerateChallenge: 'Regenerate Challenge 🔄',\n        shareChallenge: 'Share Challenge 📷',\n        loading: 'Generating... ⏳',\n        yourChallenge: 'Your Challenge',\n        team: 'Team',\n        challenges: 'Challenges',\n        objective: 'Objective 🏆',\n        squad: 'Squad 🏃',\n        tactics: 'Tactics ⚽',\n        footer: 'Created with ❤️ for the Football Manager community',\n        errorTeam: 'Could not find a team with the selected difficulty.',\n        errorChallenge: 'Could not find challenges with the selected difficulty.',\n        errorGeneric: 'An error occurred while generating the challenge.',\n        filterByContinent: 'Filter by continent:',\n        activeFilter: 'Active filter: only teams from selected continents will be chosen',\n        clickToLock: 'Click to lock',\n        locked: 'Locked',\n        aboutTitle: 'What is FM Challenger?',\n        aboutDescription: 'FM Challenger is the ultimate challenge generator for Football Manager. Select difficulty levels for teams and objectives, apply continent filters, and generate unique challenges to make your games more exciting and unpredictable.',\n        aboutFeatures: 'Key features: customizable difficulties, geographical filters, lock system for selective regeneration, challenge saving, and multilingual support.',\n        welcomeTitle: 'Welcome to FM24 Challenge Creator!',\n        welcomeSubtitle: 'This webapp allows you to generate random challenges for Football Manager 2024. Here\\'s how it works:',\n        teamDifficultyTitle: 'Team Difficulty',\n        teamDifficultyDescription: 'Select the difficulty level of the league. This will determine which teams can be selected for your challenge - from the most prestigious to the most obscure.',\n        challengeDifficultyTitle: 'Challenge Difficulty',\n        challengeDifficultyDescription: 'Choose how challenging you want the specific challenges you need to complete to be. From simple objectives to nearly impossible missions!',\n        finalDescription: 'Once you\\'ve chosen these parameters, press \"Generate Challenge\" and the system will propose a random team with three challenges to complete: one for the squad, one tactical and one seasonal objective. You can generate as many challenges as you want until you find the perfect one for you!',\n        feedbackTitle: 'Have ideas for new challenges?',\n        feedbackSubtitle: 'Leave feedback',\n        feedbackButton: 'Send Feedback',\n        // New translations for features\n        customizableDifficulties: 'Customizable Difficulties',\n        geographicalFilters: 'Geographical Filters',\n        lockSystem: 'Lock System',\n        multilingual: 'Multilingual',\n        generateChallengeToStart: 'Generate a challenge to start!',\n        veryEasy: 'Very Easy',\n        easy: 'Easy',\n        medium: 'Medium',\n        hard: 'Hard',\n        veryHard: 'Very Hard',\n        crazy: 'Crazy',\n        meme: 'Meme',\n        profile: 'Profile',\n        savedChallenges: 'Saved Challenges',\n        activeChallenges: 'Active Challenges',\n        completedChallenges: 'Completed Challenges',\n        archivedChallenges: 'Archived Challenges',\n        challengeCompleted: 'Challenge Completed',\n        archiveChallenge: 'Archive',\n        unarchiveChallenge: 'Restore',\n        shareOnFacebook: 'Share on Facebook',\n        language: 'Language'\n    }\n};\n// Creazione del context\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"LanguageProvider.useState\": ()=>{\n            // Rileva la lingua del browser\n            if (false) {}\n            return 'en'; // Default fallback\n        }\n    }[\"LanguageProvider.useState\"]);\n    const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(translations.it);\n    // Rileva la lingua del browser all'avvio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const detectBrowserLanguage = {\n                \"LanguageProvider.useEffect.detectBrowserLanguage\": ()=>{\n                    if (false) {}\n                    return 'en';\n                }\n            }[\"LanguageProvider.useEffect.detectBrowserLanguage\"];\n            const detectedLang = detectBrowserLanguage();\n            setLanguage(detectedLang);\n            setT(translations[detectedLang]);\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Aggiorna le traduzioni quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            setT(translations[language]);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Funzione per cambiare lingua\n    const changeLanguage = (lang)=>{\n        if (translations[lang]) {\n            setLanguage(lang);\n            // Salva la preferenza dell'utente\n            if (false) {}\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            t,\n            changeLanguage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\lib\\\\LanguageContext.js\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n// Hook personalizzato per utilizzare il context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/LanguageContext.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bf527086e705\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcUHJvZ2V0dGlcXGZtLWNoYWxsZW5nZXJcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZjUyNzA4NmU3MDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/contact/page.jsx":
/*!******************************!*\
  !*** ./app/contact/page.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\app\\contact\\page.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Roboto\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-roboto\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"roboto\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(rsc)/./lib/LanguageContext.js\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SessionProvider */ \"(rsc)/./components/SessionProvider.jsx\");\n/* harmony import */ var _components_Topbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Topbar */ \"(rsc)/./components/Topbar.jsx\");\n/* harmony import */ var _components_CookieBanner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/CookieBanner */ \"(rsc)/./components/CookieBanner.jsx\");\n/* harmony import */ var _components_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/GoogleAnalytics */ \"(rsc)/./components/GoogleAnalytics.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"FM Challenger - Create your challenge for FM\",\n    description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n    keywords: \"Football Manager, FM, challenge, random, generator, football, soccer, game, tactics, squad, objectives\",\n    authors: [\n        {\n            name: \"FM Challenger Team\"\n        }\n    ],\n    creator: \"FM Challenger\",\n    publisher: \"FM Challenger\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://fmchallenger.com'),\n    alternates: {\n        canonical: '/',\n        languages: {\n            'it-IT': '/it',\n            'en-US': '/en'\n        }\n    },\n    openGraph: {\n        title: \"FM Challenger - Create your challenge for FM\",\n        description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n        url: 'https://fmchallenger.com',\n        siteName: 'FM Challenger',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'FM Challenger - Football Manager Challenge Generator'\n            }\n        ],\n        locale: 'it_IT',\n        type: 'website'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: \"FM Challenger - Create your challenge for FM\",\n        description: \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n        images: [\n            '/og-image.jpg'\n        ],\n        creator: '@fmchallenger'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1A73E8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/img/favicon/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/img/favicon/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/img/favicon/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/img/favicon/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"FM Challenger\",\n                                \"description\": \"Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.\",\n                                \"url\": \"https://fmchallenger.com\",\n                                \"applicationCategory\": \"GameApplication\",\n                                \"operatingSystem\": \"Web\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"EUR\"\n                                },\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"FM Challenger Team\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_js_import_Roboto_arguments_variable_font_roboto_subsets_latin_weight_300_400_500_700_display_swap_variableName_roboto___WEBPACK_IMPORTED_MODULE_8___default().variable)} font-roboto antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        GA_MEASUREMENT_ID: \"G-XXXXXXXXXX\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.Toaster, {\n                                    position: \"top-center\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Topbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CookieBanner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\layout.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./components/CookieBanner.jsx":
/*!*************************************!*\
  !*** ./components/CookieBanner.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\CookieBanner.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\CookieBanner.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/GoogleAnalytics.jsx":
/*!****************************************!*\
  !*** ./components/GoogleAnalytics.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\GoogleAnalytics.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\GoogleAnalytics.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/SessionProvider.jsx":
/*!****************************************!*\
  !*** ./components/SessionProvider.jsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SessionProvider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\SessionProvider.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/Topbar.jsx":
/*!*******************************!*\
  !*** ./components/Topbar.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\Topbar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\components\\Topbar.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./lib/LanguageContext.js":
/*!********************************!*\
  !*** ./lib/LanguageContext.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\lib\\LanguageContext.js",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Progetti\\fm-challenger\\fm-challenger-frontend\\lib\\LanguageContext.js",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxQcm9nZXR0aVxcZm0tY2hhbGxlbmdlclxcZm0tY2hhbGxlbmdlci1mcm9udGVuZFxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.jsx&appDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDesktop%5CProgetti%5Cfm-challenger%5Cfm-challenger-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();