'use client';

import { useLanguage } from '../lib/LanguageContext';

export default function AboutSection() {
  const { t } = useLanguage();

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-textTitle mb-6 gradient-text">
          {t.welcomeTitle}
        </h1>
        <p className="text-textSecondary text-lg mb-8 max-w-4xl mx-auto">
          {t.welcomeSubtitle}
        </p>
      </div>

      {/* Due div con bordi colorati come nell'immagine */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {/* Difficoltà Campionato */}
        <div className="bg-bgBox rounded-lg p-6 border-l-4 border-accent">
          <h2 className="text-xl font-bold text-accent mb-4">{t.teamDifficultyTitle}</h2>
          <p className="text-textSecondary leading-relaxed">
            {t.teamDifficultyDescription}
          </p>
        </div>

        {/* Difficoltà Sfide */}
        <div className="bg-bgBox rounded-lg p-6 border-l-4 border-accent">
          <h2 className="text-xl font-bold text-accent mb-4">{t.challengeDifficultyTitle}</h2>
          <p className="text-textSecondary leading-relaxed">
            {t.challengeDifficultyDescription}
          </p>
        </div>
      </div>

      {/* Testo descrittivo finale */}
      <div className="text-center">
        <p className="text-textSecondary leading-relaxed max-w-4xl mx-auto">
          {t.finalDescription}
        </p>
      </div>
    </div>
  );
}
