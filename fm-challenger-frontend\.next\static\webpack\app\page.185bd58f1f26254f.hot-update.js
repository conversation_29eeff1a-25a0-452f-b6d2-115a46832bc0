"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/SelectChallenge.jsx":
/*!****************************************!*\
  !*** ./components/SelectChallenge.jsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SelectChallenge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./lib/supabase.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SelectChallenge(param) {\n    let { onGenerateChallenge } = param;\n    _s();\n    const [teamDifficulty, setTeamDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [challengeDifficulty, setChallengeDifficulty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [teamDifficultyLevels, setTeamDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [challengeDifficultyLevels, setChallengeDifficultyLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTeamDescription, setSelectedTeamDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedChallengeDescription, setSelectedChallengeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedContinents, setSelectedContinents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    // Lista dei continenti disponibili\n    const continents = [\n        {\n            value: 'Europe',\n            label: 'Europa'\n        },\n        {\n            value: 'Asia',\n            label: 'Asia'\n        },\n        {\n            value: 'North America',\n            label: 'Nord America'\n        },\n        {\n            value: 'South America',\n            label: 'Sud America'\n        },\n        {\n            value: 'Africa',\n            label: 'Africa'\n        }\n    ];\n    // Carica i livelli di difficoltà dal database quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SelectChallenge.useEffect\": ()=>{\n            const fetchDifficultyLevels = {\n                \"SelectChallenge.useEffect.fetchDifficultyLevels\": async ()=>{\n                    setLoading(true);\n                    try {\n                        // Recupera i livelli di difficoltà per le squadre\n                        const teamLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getTeamDifficultyLevels)(language);\n                        setTeamDifficultyLevels(teamLevels);\n                        // Recupera i livelli di difficoltà per le sfide\n                        const challengeLevels = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.getChallengeDifficultyLevels)(language);\n                        setChallengeDifficultyLevels(challengeLevels);\n                        // Resetta le selezioni quando cambia la lingua\n                        setTeamDifficulty('');\n                        setChallengeDifficulty('');\n                        setSelectedTeamDescription('');\n                        setSelectedChallengeDescription('');\n                    } catch (error) {\n                        console.error('Error fetching difficulty levels:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SelectChallenge.useEffect.fetchDifficultyLevels\"];\n            fetchDifficultyLevels();\n        }\n    }[\"SelectChallenge.useEffect\"], [\n        language\n    ]);\n    // Aggiorna la descrizione quando cambia la difficoltà della squadra\n    const handleTeamDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setTeamDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = teamDifficultyLevels.find((level)=>level.value === value);\n        setSelectedTeamDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Aggiorna la descrizione quando cambia la difficoltà della sfida\n    const handleChallengeDifficultyChange = (e)=>{\n        const value = e.target.value;\n        setChallengeDifficulty(value);\n        // Trova la descrizione corrispondente\n        const selectedLevel = challengeDifficultyLevels.find((level)=>level.value === value);\n        setSelectedChallengeDescription(selectedLevel ? selectedLevel.description : '');\n    };\n    // Gestisce il cambio di stato dei checkbox dei continenti\n    const handleContinentChange = (continent)=>{\n        setSelectedContinents((prev)=>{\n            if (prev.includes(continent)) {\n                return prev.filter((c)=>c !== continent);\n            } else {\n                return [\n                    ...prev,\n                    continent\n                ];\n            }\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (teamDifficulty && challengeDifficulty) {\n            onGenerateChallenge(teamDifficulty, challengeDifficulty, language, selectedContinents);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card w-full h-full bg-opacity-90\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-4 text-textTitle\",\n                children: t.teamDifficulty\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: t.loading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"teamDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.teamDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"teamDifficulty\",\n                                value: teamDifficulty,\n                                onChange: handleTeamDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.teamDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    teamDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            selectedTeamDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedTeamDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"challengeDifficulty\",\n                                className: \"block mb-2 text-textPrimary\",\n                                children: [\n                                    t.challengeDifficulty,\n                                    \":\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"challengeDifficulty\",\n                                value: challengeDifficulty,\n                                onChange: handleChallengeDifficultyChange,\n                                className: \"select-custom w-full\",\n                                required: true,\n                                \"aria-label\": t.challengeDifficulty,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: t.select\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    challengeDifficultyLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: level.value,\n                                            children: level.name\n                                        }, level.value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            selectedChallengeDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary rounded-lg p-2 border-l-4 border-accent\",\n                                children: selectedChallengeDescription\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"block mb-2 text-textPrimary font-medium\",\n                                children: t.filterByContinent\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                                children: continents.map((continent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                id: \"continent-\".concat(continent.value),\n                                                checked: selectedContinents.includes(continent.value),\n                                                onChange: ()=>handleContinentChange(continent.value),\n                                                className: \"mr-2 h-4 w-4 accent-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"continent-\".concat(continent.value),\n                                                className: \"text-textPrimary text-sm\",\n                                                children: continent.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, continent.value, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            selectedContinents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-xs text-textSecondary\",\n                                children: t.activeFilter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"btn-primary w-full mt-6\",\n                        disabled: !teamDifficulty || !challengeDifficulty,\n                        children: t.generateChallenge\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\components\\\\SelectChallenge.jsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(SelectChallenge, \"6RM6CGT09OcUtAEDpunkaBuVez8=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = SelectChallenge;\nvar _c;\n$RefreshReg$(_c, \"SelectChallenge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/SelectChallenge.jsx\n"));

/***/ })

});