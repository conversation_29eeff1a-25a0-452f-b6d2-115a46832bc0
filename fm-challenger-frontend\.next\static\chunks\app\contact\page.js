/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/contact/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.jsx */ \"(app-pages-browser)/./app/contact/page.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDdXNlciU1QyU1Q0Rlc2t0b3AlNUMlNUNQcm9nZXR0aSU1QyU1Q2ZtLWNoYWxsZW5nZXIlNUMlNUNmbS1jaGFsbGVuZ2VyLWZyb250ZW5kJTVDJTVDYXBwJTVDJTVDY29udGFjdCU1QyU1Q3BhZ2UuanN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXNJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERlc2t0b3BcXFxcUHJvZ2V0dGlcXFxcZm0tY2hhbGxlbmdlclxcXFxmbS1jaGFsbGVuZ2VyLWZyb250ZW5kXFxcXGFwcFxcXFxjb250YWN0XFxcXHBhZ2UuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/contact/page.jsx":
/*!******************************!*\
  !*** ./app/contact/page.jsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ContactPage() {\n    _s();\n    const { language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n    });\n    const content = {\n        it: {\n            title: \"Contattaci\",\n            subtitle: \"Hai domande o suggerimenti? Siamo qui per aiutarti!\",\n            form: {\n                name: \"Nome\",\n                email: \"Email\",\n                subject: \"Oggetto\",\n                message: \"Messaggio\",\n                send: \"Invia Messaggio\",\n                sending: \"Invio in corso...\",\n                success: \"Messaggio inviato con successo!\",\n                error: \"Errore nell'invio del messaggio. Riprova.\"\n            },\n            info: {\n                title: \"Altre modalità di contatto\",\n                email: \"Email: <EMAIL>\",\n                social: \"Seguici sui social media per aggiornamenti e novità\"\n            }\n        },\n        en: {\n            title: \"Contact Us\",\n            subtitle: \"Have questions or suggestions? We're here to help!\",\n            form: {\n                name: \"Name\",\n                email: \"Email\",\n                subject: \"Subject\",\n                message: \"Message\",\n                send: \"Send Message\",\n                sending: \"Sending...\",\n                success: \"Message sent successfully!\",\n                error: \"Error sending message. Please try again.\"\n            },\n            info: {\n                title: \"Other ways to contact us\",\n                email: \"Email: <EMAIL>\",\n                social: \"Follow us on social media for updates and news\"\n            }\n        }\n    };\n    const texts = content[language] || content.it;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Qui implementeresti l'invio del form\n        console.log('Form submitted:', formData);\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-bgMain text-textPrimary py-12 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-textTitle mb-4 gradient-text\",\n                            children: texts.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary text-lg max-w-2xl mx-auto\",\n                            children: texts.subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"name\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"name\",\n                                                name: \"name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                name: \"email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"subject\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"subject\",\n                                                name: \"subject\",\n                                                value: formData.subject,\n                                                onChange: handleChange,\n                                                required: true,\n                                                className: \"input-field w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"message\",\n                                                className: \"block text-textTitle font-medium mb-2\",\n                                                children: texts.form.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                name: \"message\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                required: true,\n                                                rows: 6,\n                                                className: \"input-field w-full resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary w-full\",\n                                        children: texts.form.send\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-textTitle mb-4\",\n                                            children: texts.info.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-accent\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-textSecondary\",\n                                                        children: texts.info.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-textTitle mb-4\",\n                                            children: \"Social Media\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-textSecondary mb-4\",\n                                            children: texts.info.social\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                'Facebook',\n                                                'Instagram',\n                                                'X',\n                                                'Reddit'\n                                            ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\".concat(social.toLowerCase()),\n                                                    className: \"text-textSecondary hover:text-accent transition-colors\",\n                                                    children: social\n                                                }, social, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\contact\\\\page.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactPage, \"WDBWvDm3bjBIJDzzXUOuwIloUDY=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contact/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/LanguageContext.js":
/*!********************************!*\
  !*** ./lib/LanguageContext.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Definizione delle traduzioni\nconst translations = {\n    it: {\n        title: 'FM Challenger',\n        subtitle: 'Genera sfide random per Football Manager',\n        teamDifficulty: 'Difficoltà Campionato',\n        challengeDifficulty: 'Difficoltà Sfida',\n        select: 'Seleziona...',\n        generateChallenge: 'Genera Sfida 🎲',\n        regenerateChallenge: 'Rigenera Sfida 🔄',\n        shareChallenge: 'Condividi Sfida 📷',\n        loading: 'Generazione in corso... ⏳',\n        yourChallenge: 'La Tua Sfida',\n        team: 'Squadra',\n        challenges: 'Sfide',\n        objective: 'Obiettivo 🏆',\n        squad: 'Rosa 🏃',\n        tactics: 'Tattica ⚽',\n        footer: 'Creato con ❤️ per la community di Football Manager',\n        errorTeam: 'Non è stato possibile trovare una squadra con la difficoltà selezionata.',\n        errorChallenge: 'Non è stato possibile trovare sfide con la difficoltà selezionata.',\n        errorGeneric: 'Si è verificato un errore durante la generazione della sfida.',\n        filterByContinent: 'Filtra per continente:',\n        activeFilter: 'Filtro attivo: verranno selezionate solo squadre dai continenti scelti',\n        clickToLock: 'Clicca per bloccare',\n        locked: 'Bloccato',\n        aboutTitle: 'Cos\\'è FM Challenger?',\n        aboutDescription: 'FM Challenger è il generatore di sfide definitivo per Football Manager. Seleziona il livello di difficoltà per squadre e obiettivi, applica filtri per continente e genera sfide uniche per rendere le tue partite più emozionanti e imprevedibili.',\n        aboutFeatures: 'Caratteristiche principali: difficoltà personalizzabili, filtri geografici, sistema di blocco per rigenerazioni selettive, salvataggio sfide e supporto multilingua.',\n        welcomeTitle: 'Benvenuto in FM Challenger!',\n        welcomeSubtitle: 'Questa webapp ti permette di generare sfide casuali per Football Manager 2024. Ecco come funziona:',\n        teamDifficultyTitle: 'Difficoltà Campionato',\n        teamDifficultyDescription: 'Seleziona il livello di difficoltà del campionato. Questo determinerà quali squadre potranno essere selezionate per la tua sfida - dalle più blasonate alle più oscure.',\n        challengeDifficultyTitle: 'Difficoltà Sfide',\n        challengeDifficultyDescription: 'Scegli quanto vuoi rendere impegnative le sfide specifiche che dovrai completare. Da semplici obiettivi a missioni quasi impossibili!',\n        finalDescription: 'Una volta scelti questi parametri, premi \"Genera Sfida\" e il sistema ti proporrà una squadra casuale con tre sfide da completare: una per la rosa, una tattica e un obiettivo stagionale. Puoi generare quante sfide vuoi finché non trovi quella perfetta per te!',\n        feedbackTitle: 'Hai idee per nuove sfide?',\n        feedbackSubtitle: 'Lascia un feedback',\n        feedbackButton: 'Invia Feedback',\n        // Nuove traduzioni per le funzionalità\n        customizableDifficulties: 'Difficoltà Personalizzabili',\n        geographicalFilters: 'Filtri Geografici',\n        lockSystem: 'Sistema di Blocco',\n        multilingual: 'Multilingua',\n        generateChallengeToStart: 'Genera una sfida per iniziare!',\n        veryEasy: 'Molto Facile',\n        easy: 'Facile',\n        medium: 'Media',\n        hard: 'Difficile',\n        veryHard: 'Molto Difficile',\n        crazy: 'Matta',\n        meme: 'Meme',\n        profile: 'Profilo',\n        savedChallenges: 'Sfide Salvate',\n        activeChallenges: 'Sfide Attive',\n        completedChallenges: 'Sfide Completate',\n        archivedChallenges: 'Sfide Archiviate',\n        challengeCompleted: 'Sfida Completata',\n        archiveChallenge: 'Archivia',\n        unarchiveChallenge: 'Ripristina',\n        shareOnFacebook: 'Condividi su Facebook',\n        language: 'Lingua'\n    },\n    en: {\n        title: 'FM Challenger',\n        subtitle: 'Generate random challenges for Football Manager',\n        teamDifficulty: 'Team Difficulty',\n        challengeDifficulty: 'Challenge Difficulty',\n        select: 'Select...',\n        generateChallenge: 'Generate Challenge 🎲',\n        regenerateChallenge: 'Regenerate Challenge 🔄',\n        shareChallenge: 'Share Challenge 📷',\n        loading: 'Generating... ⏳',\n        yourChallenge: 'Your Challenge',\n        team: 'Team',\n        challenges: 'Challenges',\n        objective: 'Objective 🏆',\n        squad: 'Squad 🏃',\n        tactics: 'Tactics ⚽',\n        footer: 'Created with ❤️ for the Football Manager community',\n        errorTeam: 'Could not find a team with the selected difficulty.',\n        errorChallenge: 'Could not find challenges with the selected difficulty.',\n        errorGeneric: 'An error occurred while generating the challenge.',\n        filterByContinent: 'Filter by continent:',\n        activeFilter: 'Active filter: only teams from selected continents will be chosen',\n        clickToLock: 'Click to lock',\n        locked: 'Locked',\n        aboutTitle: 'What is FM Challenger?',\n        aboutDescription: 'FM Challenger is the ultimate challenge generator for Football Manager. Select difficulty levels for teams and objectives, apply continent filters, and generate unique challenges to make your games more exciting and unpredictable.',\n        aboutFeatures: 'Key features: customizable difficulties, geographical filters, lock system for selective regeneration, challenge saving, and multilingual support.',\n        welcomeTitle: 'Welcome to FM24 Challenge Creator!',\n        welcomeSubtitle: 'This webapp allows you to generate random challenges for Football Manager 2024. Here\\'s how it works:',\n        teamDifficultyTitle: 'Team Difficulty',\n        teamDifficultyDescription: 'Select the difficulty level of the league. This will determine which teams can be selected for your challenge - from the most prestigious to the most obscure.',\n        challengeDifficultyTitle: 'Challenge Difficulty',\n        challengeDifficultyDescription: 'Choose how challenging you want the specific challenges you need to complete to be. From simple objectives to nearly impossible missions!',\n        finalDescription: 'Once you\\'ve chosen these parameters, press \"Generate Challenge\" and the system will propose a random team with three challenges to complete: one for the squad, one tactical and one seasonal objective. You can generate as many challenges as you want until you find the perfect one for you!',\n        feedbackTitle: 'Have ideas for new challenges?',\n        feedbackSubtitle: 'Leave feedback',\n        feedbackButton: 'Send Feedback',\n        // New translations for features\n        customizableDifficulties: 'Customizable Difficulties',\n        geographicalFilters: 'Geographical Filters',\n        lockSystem: 'Lock System',\n        multilingual: 'Multilingual',\n        generateChallengeToStart: 'Generate a challenge to start!',\n        veryEasy: 'Very Easy',\n        easy: 'Easy',\n        medium: 'Medium',\n        hard: 'Hard',\n        veryHard: 'Very Hard',\n        crazy: 'Crazy',\n        meme: 'Meme',\n        profile: 'Profile',\n        savedChallenges: 'Saved Challenges',\n        activeChallenges: 'Active Challenges',\n        completedChallenges: 'Completed Challenges',\n        archivedChallenges: 'Archived Challenges',\n        challengeCompleted: 'Challenge Completed',\n        archiveChallenge: 'Archive',\n        unarchiveChallenge: 'Restore',\n        shareOnFacebook: 'Share on Facebook',\n        language: 'Language'\n    }\n};\n// Creazione del context\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"LanguageProvider.useState\": ()=>{\n            // Rileva la lingua del browser\n            if (true) {\n                const browserLang = navigator.language || navigator.languages[0];\n                const langCode = browserLang.split('-')[0]; // Prende solo il codice lingua (es: 'it' da 'it-IT')\n                // Controlla se la lingua rilevata è supportata, altrimenti usa inglese\n                return langCode === 'it' ? 'it' : 'en';\n            }\n            return 'en'; // Default fallback\n        }\n    }[\"LanguageProvider.useState\"]);\n    const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(translations.it);\n    // Rileva la lingua del browser all'avvio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const detectBrowserLanguage = {\n                \"LanguageProvider.useEffect.detectBrowserLanguage\": ()=>{\n                    if (true) {\n                        const browserLang = navigator.language.split('-')[0];\n                        return translations[browserLang] ? browserLang : 'en';\n                    }\n                    return 'en';\n                }\n            }[\"LanguageProvider.useEffect.detectBrowserLanguage\"];\n            const detectedLang = detectBrowserLanguage();\n            setLanguage(detectedLang);\n            setT(translations[detectedLang]);\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Aggiorna le traduzioni quando cambia la lingua\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            setT(translations[language]);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Funzione per cambiare lingua\n    const changeLanguage = (lang)=>{\n        if (translations[lang]) {\n            setLanguage(lang);\n            // Salva la preferenza dell'utente\n            if (true) {\n                localStorage.setItem('preferredLanguage', lang);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            t,\n            changeLanguage\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\lib\\\\LanguageContext.js\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"GaB8w7XTshBE/U77NA0oQ/2vWkI=\");\n_c = LanguageProvider;\n// Hook personalizzato per utilizzare il context\nfunction useLanguage() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/LanguageContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE$2\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function disabledLog() {}\n    function disableLogs() {\n      if (0 === disabledDepth) {\n        prevLog = console.log;\n        prevInfo = console.info;\n        prevWarn = console.warn;\n        prevError = console.error;\n        prevGroup = console.group;\n        prevGroupCollapsed = console.groupCollapsed;\n        prevGroupEnd = console.groupEnd;\n        var props = {\n          configurable: !0,\n          enumerable: !0,\n          value: disabledLog,\n          writable: !0\n        };\n        Object.defineProperties(console, {\n          info: props,\n          log: props,\n          warn: props,\n          error: props,\n          group: props,\n          groupCollapsed: props,\n          groupEnd: props\n        });\n      }\n      disabledDepth++;\n    }\n    function reenableLogs() {\n      disabledDepth--;\n      if (0 === disabledDepth) {\n        var props = { configurable: !0, enumerable: !0, writable: !0 };\n        Object.defineProperties(console, {\n          log: assign({}, props, { value: prevLog }),\n          info: assign({}, props, { value: prevInfo }),\n          warn: assign({}, props, { value: prevWarn }),\n          error: assign({}, props, { value: prevError }),\n          group: assign({}, props, { value: prevGroup }),\n          groupCollapsed: assign({}, props, { value: prevGroupCollapsed }),\n          groupEnd: assign({}, props, { value: prevGroupEnd })\n        });\n      }\n      0 > disabledDepth &&\n        console.error(\n          \"disabledDepth fell below zero. This is a bug in React. Please file an issue.\"\n        );\n    }\n    function describeBuiltInComponentFrame(name) {\n      if (void 0 === prefix)\n        try {\n          throw Error();\n        } catch (x) {\n          var match = x.stack.trim().match(/\\n( *(at )?)/);\n          prefix = (match && match[1]) || \"\";\n          suffix =\n            -1 < x.stack.indexOf(\"\\n    at\")\n              ? \" (<anonymous>)\"\n              : -1 < x.stack.indexOf(\"@\")\n                ? \"@unknown:0:0\"\n                : \"\";\n        }\n      return \"\\n\" + prefix + name + suffix;\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      if (!fn || reentry) return \"\";\n      var frame = componentFrameCache.get(fn);\n      if (void 0 !== frame) return frame;\n      reentry = !0;\n      frame = Error.prepareStackTrace;\n      Error.prepareStackTrace = void 0;\n      var previousDispatcher = null;\n      previousDispatcher = ReactSharedInternals.H;\n      ReactSharedInternals.H = null;\n      disableLogs();\n      try {\n        var RunInRootFrame = {\n          DetermineComponentFrameRoot: function () {\n            try {\n              if (construct) {\n                var Fake = function () {\n                  throw Error();\n                };\n                Object.defineProperty(Fake.prototype, \"props\", {\n                  set: function () {\n                    throw Error();\n                  }\n                });\n                if (\"object\" === typeof Reflect && Reflect.construct) {\n                  try {\n                    Reflect.construct(Fake, []);\n                  } catch (x) {\n                    var control = x;\n                  }\n                  Reflect.construct(fn, [], Fake);\n                } else {\n                  try {\n                    Fake.call();\n                  } catch (x$0) {\n                    control = x$0;\n                  }\n                  fn.call(Fake.prototype);\n                }\n              } else {\n                try {\n                  throw Error();\n                } catch (x$1) {\n                  control = x$1;\n                }\n                (Fake = fn()) &&\n                  \"function\" === typeof Fake.catch &&\n                  Fake.catch(function () {});\n              }\n            } catch (sample) {\n              if (sample && control && \"string\" === typeof sample.stack)\n                return [sample.stack, control.stack];\n            }\n            return [null, null];\n          }\n        };\n        RunInRootFrame.DetermineComponentFrameRoot.displayName =\n          \"DetermineComponentFrameRoot\";\n        var namePropDescriptor = Object.getOwnPropertyDescriptor(\n          RunInRootFrame.DetermineComponentFrameRoot,\n          \"name\"\n        );\n        namePropDescriptor &&\n          namePropDescriptor.configurable &&\n          Object.defineProperty(\n            RunInRootFrame.DetermineComponentFrameRoot,\n            \"name\",\n            { value: \"DetermineComponentFrameRoot\" }\n          );\n        var _RunInRootFrame$Deter =\n            RunInRootFrame.DetermineComponentFrameRoot(),\n          sampleStack = _RunInRootFrame$Deter[0],\n          controlStack = _RunInRootFrame$Deter[1];\n        if (sampleStack && controlStack) {\n          var sampleLines = sampleStack.split(\"\\n\"),\n            controlLines = controlStack.split(\"\\n\");\n          for (\n            _RunInRootFrame$Deter = namePropDescriptor = 0;\n            namePropDescriptor < sampleLines.length &&\n            !sampleLines[namePropDescriptor].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            namePropDescriptor++;\n          for (\n            ;\n            _RunInRootFrame$Deter < controlLines.length &&\n            !controlLines[_RunInRootFrame$Deter].includes(\n              \"DetermineComponentFrameRoot\"\n            );\n\n          )\n            _RunInRootFrame$Deter++;\n          if (\n            namePropDescriptor === sampleLines.length ||\n            _RunInRootFrame$Deter === controlLines.length\n          )\n            for (\n              namePropDescriptor = sampleLines.length - 1,\n                _RunInRootFrame$Deter = controlLines.length - 1;\n              1 <= namePropDescriptor &&\n              0 <= _RunInRootFrame$Deter &&\n              sampleLines[namePropDescriptor] !==\n                controlLines[_RunInRootFrame$Deter];\n\n            )\n              _RunInRootFrame$Deter--;\n          for (\n            ;\n            1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter;\n            namePropDescriptor--, _RunInRootFrame$Deter--\n          )\n            if (\n              sampleLines[namePropDescriptor] !==\n              controlLines[_RunInRootFrame$Deter]\n            ) {\n              if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n                do\n                  if (\n                    (namePropDescriptor--,\n                    _RunInRootFrame$Deter--,\n                    0 > _RunInRootFrame$Deter ||\n                      sampleLines[namePropDescriptor] !==\n                        controlLines[_RunInRootFrame$Deter])\n                  ) {\n                    var _frame =\n                      \"\\n\" +\n                      sampleLines[namePropDescriptor].replace(\n                        \" at new \",\n                        \" at \"\n                      );\n                    fn.displayName &&\n                      _frame.includes(\"<anonymous>\") &&\n                      (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n                    \"function\" === typeof fn &&\n                      componentFrameCache.set(fn, _frame);\n                    return _frame;\n                  }\n                while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n              }\n              break;\n            }\n        }\n      } finally {\n        (reentry = !1),\n          (ReactSharedInternals.H = previousDispatcher),\n          reenableLogs(),\n          (Error.prepareStackTrace = frame);\n      }\n      sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\")\n        ? describeBuiltInComponentFrame(sampleLines)\n        : \"\";\n      \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n      return sampleLines;\n    }\n    function describeUnknownElementTypeFrameInDEV(type) {\n      if (null == type) return \"\";\n      if (\"function\" === typeof type) {\n        var prototype = type.prototype;\n        return describeNativeComponentFrame(\n          type,\n          !(!prototype || !prototype.isReactComponent)\n        );\n      }\n      if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame(\"Suspense\");\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame(\"SuspenseList\");\n      }\n      if (\"object\" === typeof type)\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return (type = describeNativeComponentFrame(type.render, !1)), type;\n          case REACT_MEMO_TYPE:\n            return describeUnknownElementTypeFrameInDEV(type.type);\n          case REACT_LAZY_TYPE:\n            prototype = type._payload;\n            type = type._init;\n            try {\n              return describeUnknownElementTypeFrameInDEV(type(prototype));\n            } catch (x) {}\n        }\n      return \"\";\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, self, source, owner, props) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      if (\n        \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        type === REACT_OFFSCREEN_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE$1 ||\n            void 0 !== type.getModuleId))\n      ) {\n        var children = config.children;\n        if (void 0 !== children)\n          if (isStaticChildren)\n            if (isArrayImpl(children)) {\n              for (\n                isStaticChildren = 0;\n                isStaticChildren < children.length;\n                isStaticChildren++\n              )\n                validateChildKeys(children[isStaticChildren], type);\n              Object.freeze && Object.freeze(children);\n            } else\n              console.error(\n                \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n              );\n          else validateChildKeys(children, type);\n      } else {\n        children = \"\";\n        if (\n          void 0 === type ||\n          (\"object\" === typeof type &&\n            null !== type &&\n            0 === Object.keys(type).length)\n        )\n          children +=\n            \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n        null === type\n          ? (isStaticChildren = \"null\")\n          : isArrayImpl(type)\n            ? (isStaticChildren = \"array\")\n            : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE\n              ? ((isStaticChildren =\n                  \"<\" +\n                  (getComponentNameFromType(type.type) || \"Unknown\") +\n                  \" />\"),\n                (children =\n                  \" Did you accidentally export a JSX literal instead of a component?\"))\n              : (isStaticChildren = typeof type);\n        console.error(\n          \"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",\n          isStaticChildren,\n          children\n        );\n      }\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(type, children, self, source, getOwner(), maybeKey);\n    }\n    function validateChildKeys(node, parentType) {\n      if (\n        \"object\" === typeof node &&\n        node &&\n        node.$$typeof !== REACT_CLIENT_REFERENCE\n      )\n        if (isArrayImpl(node))\n          for (var i = 0; i < node.length; i++) {\n            var child = node[i];\n            isValidElement(child) && validateExplicitKey(child, parentType);\n          }\n        else if (isValidElement(node))\n          node._store && (node._store.validated = 1);\n        else if (\n          (null === node || \"object\" !== typeof node\n            ? (i = null)\n            : ((i =\n                (MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL]) ||\n                node[\"@@iterator\"]),\n              (i = \"function\" === typeof i ? i : null)),\n          \"function\" === typeof i &&\n            i !== node.entries &&\n            ((i = i.call(node)), i !== node))\n        )\n          for (; !(node = i.next()).done; )\n            isValidElement(node.value) &&\n              validateExplicitKey(node.value, parentType);\n    }\n    function isValidElement(object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    }\n    function validateExplicitKey(element, parentType) {\n      if (\n        element._store &&\n        !element._store.validated &&\n        null == element.key &&\n        ((element._store.validated = 1),\n        (parentType = getCurrentComponentErrorInfo(parentType)),\n        !ownerHasKeyUseWarning[parentType])\n      ) {\n        ownerHasKeyUseWarning[parentType] = !0;\n        var childOwner = \"\";\n        element &&\n          null != element._owner &&\n          element._owner !== getOwner() &&\n          ((childOwner = null),\n          \"number\" === typeof element._owner.tag\n            ? (childOwner = getComponentNameFromType(element._owner.type))\n            : \"string\" === typeof element._owner.name &&\n              (childOwner = element._owner.name),\n          (childOwner = \" It was passed a child from \" + childOwner + \".\"));\n        var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n        ReactSharedInternals.getCurrentStack = function () {\n          var stack = describeUnknownElementTypeFrameInDEV(element.type);\n          prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n          return stack;\n        };\n        console.error(\n          'Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.',\n          parentType,\n          childOwner\n        );\n        ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n      }\n    }\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = \"\",\n        owner = getOwner();\n      owner &&\n        (owner = getComponentNameFromType(owner.type)) &&\n        (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n      info ||\n        ((parentType = getComponentNameFromType(parentType)) &&\n          (info =\n            \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\"));\n      return info;\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n      MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n      REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      assign = Object.assign,\n      REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n      isArrayImpl = Array.isArray,\n      disabledDepth = 0,\n      prevLog,\n      prevInfo,\n      prevWarn,\n      prevError,\n      prevGroup,\n      prevGroupCollapsed,\n      prevGroupEnd;\n    disabledLog.__reactDisabledLog = !0;\n    var prefix,\n      suffix,\n      reentry = !1;\n    var componentFrameCache = new (\n      \"function\" === typeof WeakMap ? WeakMap : Map\n    )();\n    var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {},\n      ownerHasKeyUseWarning = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcUHJvZ2V0dGlcXGZtLWNoYWxsZW5nZXJcXGZtLWNoYWxsZW5nZXItZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cuser%5C%5CDesktop%5C%5CProgetti%5C%5Cfm-challenger%5C%5Cfm-challenger-frontend%5C%5Capp%5C%5Ccontact%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);