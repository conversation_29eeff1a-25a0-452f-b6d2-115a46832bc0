"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.js":
/*!*********************!*\
  !*** ./app/page.js ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AboutSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AboutSection */ \"(app-pages-browser)/./components/AboutSection.jsx\");\n/* harmony import */ var _components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/SelectChallenge */ \"(app-pages-browser)/./components/SelectChallenge.jsx\");\n/* harmony import */ var _components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ShowChallenge */ \"(app-pages-browser)/./components/ShowChallenge.jsx\");\n/* harmony import */ var _components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/SocialAndFeedback */ \"(app-pages-browser)/./components/SocialAndFeedback.jsx\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./lib/supabase.js\");\n/* harmony import */ var _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/LanguageContext */ \"(app-pages-browser)/./lib/LanguageContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { t, language } = (0,_lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__.useLanguage)();\n    const [team, setTeam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [challenges, setChallenges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDifficulties, setSelectedDifficulties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: '',\n        challenge: ''\n    });\n    // Funzione per tracciare eventi GA\n    const trackEvent = function(eventName) {\n        let eventParams = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if ( true && window.gtag) {\n            window.gtag('event', eventName, eventParams);\n        }\n    };\n    const generateChallenge = async function(teamDifficulty, challengeDifficulty) {\n        let lang = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'it', continents = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : [];\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di generazione sfida\n        trackEvent('generate_challenge', {\n            team_difficulty: teamDifficulty,\n            challenge_difficulty: challengeDifficulty,\n            language: lang\n        });\n        // Salva le difficoltà selezionate per poterle riutilizzare durante la rigenerazione\n        setSelectedDifficulties({\n            team: teamDifficulty,\n            challenge: challengeDifficulty,\n            continents: continents\n        });\n        try {\n            console.log(\"Generating challenge with team difficulty: \".concat(teamDifficulty, \", challenge difficulty: \").concat(challengeDifficulty, \", language: \").concat(lang, \", continents: \").concat(continents.join(', ')));\n            // Get random team based on difficulty and continents\n            const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(teamDifficulty, continents);\n            // Get random challenges based on difficulty\n            const randomChallenges = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getChallenges)(challengeDifficulty, lang);\n            if (!randomTeam) {\n                throw new Error(t.errorTeam);\n            }\n            if (!randomChallenges || Object.keys(randomChallenges).length === 0) {\n                throw new Error(t.errorChallenge);\n            }\n            setTeam(randomTeam);\n            setChallenges(randomChallenges);\n        } catch (err) {\n            console.error('Error generating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stato per tenere traccia degli elementi bloccati\n    const [lockedItems, setLockedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        team: false,\n        obiettivi: false,\n        rosa: false,\n        tattica: false\n    });\n    const regenerateChallenge = async (currentLockedItems)=>{\n        setLoading(true);\n        setError(null);\n        // Traccia l'evento di rigenerazione sfida\n        trackEvent('regenerate_challenge', {\n            team_difficulty: selectedDifficulties.team,\n            challenge_difficulty: selectedDifficulties.challenge,\n            language: language\n        });\n        // Aggiorniamo lo stato dei lock con quello corrente\n        setLockedItems(currentLockedItems);\n        try {\n            let newTeam = team;\n            let newChallenges = {\n                ...challenges\n            };\n            // If team is not locked, get a new random team\n            if (!currentLockedItems.team && team) {\n                // Usa la difficoltà e i continenti salvati in precedenza\n                const continents = selectedDifficulties.continents || [];\n                const randomTeam = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomTeam)(selectedDifficulties.team, continents);\n                if (randomTeam) {\n                    newTeam = randomTeam;\n                }\n            }\n            // For each challenge category that is not locked, get a new random challenge\n            for (const category of [\n                'obiettivi',\n                'rosa',\n                'tattica'\n            ]){\n                if (!currentLockedItems[category] && challenges && challenges[category]) {\n                    try {\n                        // Utilizziamo la funzione esistente per ottenere una nuova sfida casuale\n                        const newChallenge = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_6__.getRandomChallenge)(selectedDifficulties.challenge, category, language);\n                        if (newChallenge && newChallenge.id !== challenges[category].id) {\n                            newChallenges[category] = newChallenge;\n                        }\n                    } catch (err) {\n                        console.error(\"Error regenerating challenge for category \".concat(category, \":\"), err);\n                    }\n                }\n            }\n            setTeam(newTeam);\n            setChallenges(newChallenges);\n        } catch (err) {\n            console.error('Error regenerating challenge:', err);\n            setError(err.message || t.errorGeneric);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const shareChallenge = async ()=>{\n        // Traccia l'evento di condivisione sfida\n        trackEvent('share_challenge', {\n            team_name: (team === null || team === void 0 ? void 0 : team.name) || 'unknown',\n            language: language\n        });\n    // Resto del codice esistente...\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-bgMain text-textPrimary\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg p-6 md:p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 md:px-8 \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-containerBg p-6 md:p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 grid lg:grid-cols-2 gap-8 border-t border-textSecondary/10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectChallenge__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onGenerateChallenge: generateChallenge\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-[400px] flex flex-col justify-center\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-textPrimary bg-bgBox\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-accent\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        t.loading\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this),\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"h-5 w-5 text-red-400\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            team && challenges && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShowChallenge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                team: team,\n                                                challenges: challenges,\n                                                onRegenerate: regenerateChallenge,\n                                                initialLockedState: lockedItems\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this),\n                                            !team && !challenges && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-textSecondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1,\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg\",\n                                                            children: t.generateChallengeToStart\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 border-t border-textSecondary/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SocialAndFeedback__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-secondary rounded-xl p-6 border-t border-textSecondary/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-textSecondary text-sm\",\n                            children: [\n                                \"FM Challenger \\xa9 \",\n                                new Date().getFullYear(),\n                                \" - \",\n                                t.footer\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex justify-center space-x-4 text-xs text-textSecondary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/privacy\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Privacy Policy' : 'Privacy Policy'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/terms\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Termini di Servizio' : 'Terms of Service'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/contact\",\n                                    className: \"hover:text-textPrimary transition-colors\",\n                                    children: language === 'it' ? 'Contatti' : 'Contact'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Progetti\\\\fm-challenger\\\\fm-challenger-frontend\\\\app\\\\page.js\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"4eWGB69s2Clp1cdwwlwDWBVteDA=\", false, function() {\n    return [\n        _lib_LanguageContext__WEBPACK_IMPORTED_MODULE_7__.useLanguage\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.js\n"));

/***/ })

});